{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc --noEmit", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "db:push": "prisma db push --schema ./prisma/schema", "db:studio": "prisma studio", "db:generate": "prisma generate --schema ./prisma/schema", "db:migrate": "prisma migrate dev"}, "prisma": {"schema": "./schema"}, "dependencies": {"dotenv": "^16.4.7", "zod": "^4.0.2", "@trpc/server": "^11.4.2", "@trpc/client": "^11.4.2", "@hono/trpc-server": "^0.4.0", "hono": "^4.8.2", "@hono/node-server": "^1.14.4", "@prisma/client": "^6.9.0", "better-auth": "^1.2.10"}, "devDependencies": {"tsdown": "^0.12.9", "typescript": "^5.8.2", "tsx": "^4.19.2", "@types/node": "^22.13.11", "prisma": "^6.9.0"}}