interface PageHeaderProps {
  title: string;
  description: string;
}

export default function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <div className="flex flex-row items-center justify-between">
      <div className="flex flex-col">
        <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0">
          {title}
        </h2>
        <p className="leading-7 text-muted-foreground">
          {description}
        </p>
      </div>
    </div>
  );
}
