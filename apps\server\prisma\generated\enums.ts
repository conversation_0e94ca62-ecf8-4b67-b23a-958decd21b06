
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export const OrderStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  REFUNDED: 'REFUNDED',
  CANCELLED: 'CANCELLED'
} as const

export type OrderStatus = (typeof OrderStatus)[keyof typeof OrderStatus]


export const StockStatus = {
  AVAILABLE: 'AVAILABLE',
  ASSIGNED: 'ASSIGNED',
  SOLD: 'SOLD'
} as const

export type StockStatus = (typeof StockStatus)[keyof typeof StockStatus]
