
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Order` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Order
 * 
 */
export type OrderModel = runtime.Types.Result.DefaultSelection<Prisma.$OrderPayload>

export type AggregateOrder = {
  _count: OrderCountAggregateOutputType | null
  _avg: OrderAvgAggregateOutputType | null
  _sum: OrderSumAggregateOutputType | null
  _min: OrderMinAggregateOutputType | null
  _max: OrderMaxAggregateOutputType | null
}

export type OrderAvgAggregateOutputType = {
  quantity: number | null
}

export type OrderSumAggregateOutputType = {
  quantity: number | null
}

export type OrderMinAggregateOutputType = {
  id: string | null
  invoiceNumber: string | null
  quantity: number | null
  status: $Enums.OrderStatus | null
  buyerEmail: string | null
  buyerWhatsapp: string | null
  variantId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type OrderMaxAggregateOutputType = {
  id: string | null
  invoiceNumber: string | null
  quantity: number | null
  status: $Enums.OrderStatus | null
  buyerEmail: string | null
  buyerWhatsapp: string | null
  variantId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type OrderCountAggregateOutputType = {
  id: number
  invoiceNumber: number
  quantity: number
  status: number
  buyerEmail: number
  buyerWhatsapp: number
  deliveredCredentials: number
  variantId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type OrderAvgAggregateInputType = {
  quantity?: true
}

export type OrderSumAggregateInputType = {
  quantity?: true
}

export type OrderMinAggregateInputType = {
  id?: true
  invoiceNumber?: true
  quantity?: true
  status?: true
  buyerEmail?: true
  buyerWhatsapp?: true
  variantId?: true
  createdAt?: true
  updatedAt?: true
}

export type OrderMaxAggregateInputType = {
  id?: true
  invoiceNumber?: true
  quantity?: true
  status?: true
  buyerEmail?: true
  buyerWhatsapp?: true
  variantId?: true
  createdAt?: true
  updatedAt?: true
}

export type OrderCountAggregateInputType = {
  id?: true
  invoiceNumber?: true
  quantity?: true
  status?: true
  buyerEmail?: true
  buyerWhatsapp?: true
  deliveredCredentials?: true
  variantId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type OrderAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Order to aggregate.
   */
  where?: Prisma.OrderWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Orders to fetch.
   */
  orderBy?: Prisma.OrderOrderByWithRelationInput | Prisma.OrderOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.OrderWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Orders from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Orders.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Orders
  **/
  _count?: true | OrderCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: OrderAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: OrderSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: OrderMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: OrderMaxAggregateInputType
}

export type GetOrderAggregateType<T extends OrderAggregateArgs> = {
      [P in keyof T & keyof AggregateOrder]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateOrder[P]>
    : Prisma.GetScalarType<T[P], AggregateOrder[P]>
}




export type OrderGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.OrderWhereInput
  orderBy?: Prisma.OrderOrderByWithAggregationInput | Prisma.OrderOrderByWithAggregationInput[]
  by: Prisma.OrderScalarFieldEnum[] | Prisma.OrderScalarFieldEnum
  having?: Prisma.OrderScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: OrderCountAggregateInputType | true
  _avg?: OrderAvgAggregateInputType
  _sum?: OrderSumAggregateInputType
  _min?: OrderMinAggregateInputType
  _max?: OrderMaxAggregateInputType
}

export type OrderGroupByOutputType = {
  id: string
  invoiceNumber: string
  quantity: number
  status: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials: runtime.JsonValue[]
  variantId: string
  createdAt: Date
  updatedAt: Date
  _count: OrderCountAggregateOutputType | null
  _avg: OrderAvgAggregateOutputType | null
  _sum: OrderSumAggregateOutputType | null
  _min: OrderMinAggregateOutputType | null
  _max: OrderMaxAggregateOutputType | null
}

type GetOrderGroupByPayload<T extends OrderGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<OrderGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof OrderGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], OrderGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], OrderGroupByOutputType[P]>
      }
    >
  > 



export type OrderWhereInput = {
  AND?: Prisma.OrderWhereInput | Prisma.OrderWhereInput[]
  OR?: Prisma.OrderWhereInput[]
  NOT?: Prisma.OrderWhereInput | Prisma.OrderWhereInput[]
  id?: Prisma.StringFilter<"Order"> | string
  invoiceNumber?: Prisma.StringFilter<"Order"> | string
  quantity?: Prisma.IntFilter<"Order"> | number
  status?: Prisma.EnumOrderStatusFilter<"Order"> | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFilter<"Order"> | string
  buyerWhatsapp?: Prisma.StringFilter<"Order"> | string
  deliveredCredentials?: Prisma.JsonNullableListFilter<"Order">
  variantId?: Prisma.StringFilter<"Order"> | string
  createdAt?: Prisma.DateTimeFilter<"Order"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Order"> | Date | string
  variant?: Prisma.XOR<Prisma.VariantScalarRelationFilter, Prisma.VariantWhereInput>
  stocks?: Prisma.StockListRelationFilter
}

export type OrderOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  status?: Prisma.SortOrder
  buyerEmail?: Prisma.SortOrder
  buyerWhatsapp?: Prisma.SortOrder
  deliveredCredentials?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  variant?: Prisma.VariantOrderByWithRelationInput
  stocks?: Prisma.StockOrderByRelationAggregateInput
}

export type OrderWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  invoiceNumber?: string
  AND?: Prisma.OrderWhereInput | Prisma.OrderWhereInput[]
  OR?: Prisma.OrderWhereInput[]
  NOT?: Prisma.OrderWhereInput | Prisma.OrderWhereInput[]
  quantity?: Prisma.IntFilter<"Order"> | number
  status?: Prisma.EnumOrderStatusFilter<"Order"> | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFilter<"Order"> | string
  buyerWhatsapp?: Prisma.StringFilter<"Order"> | string
  deliveredCredentials?: Prisma.JsonNullableListFilter<"Order">
  variantId?: Prisma.StringFilter<"Order"> | string
  createdAt?: Prisma.DateTimeFilter<"Order"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Order"> | Date | string
  variant?: Prisma.XOR<Prisma.VariantScalarRelationFilter, Prisma.VariantWhereInput>
  stocks?: Prisma.StockListRelationFilter
}, "id" | "invoiceNumber">

export type OrderOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  status?: Prisma.SortOrder
  buyerEmail?: Prisma.SortOrder
  buyerWhatsapp?: Prisma.SortOrder
  deliveredCredentials?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.OrderCountOrderByAggregateInput
  _avg?: Prisma.OrderAvgOrderByAggregateInput
  _max?: Prisma.OrderMaxOrderByAggregateInput
  _min?: Prisma.OrderMinOrderByAggregateInput
  _sum?: Prisma.OrderSumOrderByAggregateInput
}

export type OrderScalarWhereWithAggregatesInput = {
  AND?: Prisma.OrderScalarWhereWithAggregatesInput | Prisma.OrderScalarWhereWithAggregatesInput[]
  OR?: Prisma.OrderScalarWhereWithAggregatesInput[]
  NOT?: Prisma.OrderScalarWhereWithAggregatesInput | Prisma.OrderScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Order"> | string
  invoiceNumber?: Prisma.StringWithAggregatesFilter<"Order"> | string
  quantity?: Prisma.IntWithAggregatesFilter<"Order"> | number
  status?: Prisma.EnumOrderStatusWithAggregatesFilter<"Order"> | $Enums.OrderStatus
  buyerEmail?: Prisma.StringWithAggregatesFilter<"Order"> | string
  buyerWhatsapp?: Prisma.StringWithAggregatesFilter<"Order"> | string
  deliveredCredentials?: Prisma.JsonNullableListFilter<"Order">
  variantId?: Prisma.StringWithAggregatesFilter<"Order"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Order"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Order"> | Date | string
}

export type OrderCreateInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Date | string
  updatedAt?: Date | string
  variant: Prisma.VariantCreateNestedOneWithoutOrdersInput
  stocks?: Prisma.StockCreateNestedManyWithoutOrderInput
}

export type OrderUncheckedCreateInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  variantId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  stocks?: Prisma.StockUncheckedCreateNestedManyWithoutOrderInput
}

export type OrderUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  variant?: Prisma.VariantUpdateOneRequiredWithoutOrdersNestedInput
  stocks?: Prisma.StockUpdateManyWithoutOrderNestedInput
}

export type OrderUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  variantId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  stocks?: Prisma.StockUncheckedUpdateManyWithoutOrderNestedInput
}

export type OrderCreateManyInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  variantId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type OrderUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type OrderUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  variantId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type JsonNullableListFilter<$PrismaModel = never> =
| Prisma.PatchUndefined<
    Prisma.Either<Required<JsonNullableListFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableListFilterBase<$PrismaModel>>, 'path'>>,
    Required<JsonNullableListFilterBase<$PrismaModel>>
  >
| Prisma.OptionalFlat<Omit<Required<JsonNullableListFilterBase<$PrismaModel>>, 'path'>>

export type JsonNullableListFilterBase<$PrismaModel = never> = {
  equals?: runtime.InputJsonValue[] | Prisma.ListJsonFieldRefInput<$PrismaModel> | null
  has?: runtime.InputJsonValue | Prisma.JsonFieldRefInput<$PrismaModel> | null
  hasEvery?: runtime.InputJsonValue[] | Prisma.ListJsonFieldRefInput<$PrismaModel>
  hasSome?: runtime.InputJsonValue[] | Prisma.ListJsonFieldRefInput<$PrismaModel>
  isEmpty?: boolean
}

export type OrderCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  status?: Prisma.SortOrder
  buyerEmail?: Prisma.SortOrder
  buyerWhatsapp?: Prisma.SortOrder
  deliveredCredentials?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type OrderAvgOrderByAggregateInput = {
  quantity?: Prisma.SortOrder
}

export type OrderMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  status?: Prisma.SortOrder
  buyerEmail?: Prisma.SortOrder
  buyerWhatsapp?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type OrderMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  quantity?: Prisma.SortOrder
  status?: Prisma.SortOrder
  buyerEmail?: Prisma.SortOrder
  buyerWhatsapp?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type OrderSumOrderByAggregateInput = {
  quantity?: Prisma.SortOrder
}

export type OrderListRelationFilter = {
  every?: Prisma.OrderWhereInput
  some?: Prisma.OrderWhereInput
  none?: Prisma.OrderWhereInput
}

export type OrderOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type OrderNullableScalarRelationFilter = {
  is?: Prisma.OrderWhereInput | null
  isNot?: Prisma.OrderWhereInput | null
}

export type OrderCreatedeliveredCredentialsInput = {
  set: runtime.InputJsonValue[]
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type EnumOrderStatusFieldUpdateOperationsInput = {
  set?: $Enums.OrderStatus
}

export type OrderUpdatedeliveredCredentialsInput = {
  set?: runtime.InputJsonValue[]
  push?: runtime.InputJsonValue | runtime.InputJsonValue[]
}

export type OrderCreateNestedManyWithoutVariantInput = {
  create?: Prisma.XOR<Prisma.OrderCreateWithoutVariantInput, Prisma.OrderUncheckedCreateWithoutVariantInput> | Prisma.OrderCreateWithoutVariantInput[] | Prisma.OrderUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.OrderCreateOrConnectWithoutVariantInput | Prisma.OrderCreateOrConnectWithoutVariantInput[]
  createMany?: Prisma.OrderCreateManyVariantInputEnvelope
  connect?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
}

export type OrderUncheckedCreateNestedManyWithoutVariantInput = {
  create?: Prisma.XOR<Prisma.OrderCreateWithoutVariantInput, Prisma.OrderUncheckedCreateWithoutVariantInput> | Prisma.OrderCreateWithoutVariantInput[] | Prisma.OrderUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.OrderCreateOrConnectWithoutVariantInput | Prisma.OrderCreateOrConnectWithoutVariantInput[]
  createMany?: Prisma.OrderCreateManyVariantInputEnvelope
  connect?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
}

export type OrderUpdateManyWithoutVariantNestedInput = {
  create?: Prisma.XOR<Prisma.OrderCreateWithoutVariantInput, Prisma.OrderUncheckedCreateWithoutVariantInput> | Prisma.OrderCreateWithoutVariantInput[] | Prisma.OrderUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.OrderCreateOrConnectWithoutVariantInput | Prisma.OrderCreateOrConnectWithoutVariantInput[]
  upsert?: Prisma.OrderUpsertWithWhereUniqueWithoutVariantInput | Prisma.OrderUpsertWithWhereUniqueWithoutVariantInput[]
  createMany?: Prisma.OrderCreateManyVariantInputEnvelope
  set?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  disconnect?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  delete?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  connect?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  update?: Prisma.OrderUpdateWithWhereUniqueWithoutVariantInput | Prisma.OrderUpdateWithWhereUniqueWithoutVariantInput[]
  updateMany?: Prisma.OrderUpdateManyWithWhereWithoutVariantInput | Prisma.OrderUpdateManyWithWhereWithoutVariantInput[]
  deleteMany?: Prisma.OrderScalarWhereInput | Prisma.OrderScalarWhereInput[]
}

export type OrderUncheckedUpdateManyWithoutVariantNestedInput = {
  create?: Prisma.XOR<Prisma.OrderCreateWithoutVariantInput, Prisma.OrderUncheckedCreateWithoutVariantInput> | Prisma.OrderCreateWithoutVariantInput[] | Prisma.OrderUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.OrderCreateOrConnectWithoutVariantInput | Prisma.OrderCreateOrConnectWithoutVariantInput[]
  upsert?: Prisma.OrderUpsertWithWhereUniqueWithoutVariantInput | Prisma.OrderUpsertWithWhereUniqueWithoutVariantInput[]
  createMany?: Prisma.OrderCreateManyVariantInputEnvelope
  set?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  disconnect?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  delete?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  connect?: Prisma.OrderWhereUniqueInput | Prisma.OrderWhereUniqueInput[]
  update?: Prisma.OrderUpdateWithWhereUniqueWithoutVariantInput | Prisma.OrderUpdateWithWhereUniqueWithoutVariantInput[]
  updateMany?: Prisma.OrderUpdateManyWithWhereWithoutVariantInput | Prisma.OrderUpdateManyWithWhereWithoutVariantInput[]
  deleteMany?: Prisma.OrderScalarWhereInput | Prisma.OrderScalarWhereInput[]
}

export type OrderCreateNestedOneWithoutStocksInput = {
  create?: Prisma.XOR<Prisma.OrderCreateWithoutStocksInput, Prisma.OrderUncheckedCreateWithoutStocksInput>
  connectOrCreate?: Prisma.OrderCreateOrConnectWithoutStocksInput
  connect?: Prisma.OrderWhereUniqueInput
}

export type OrderUpdateOneWithoutStocksNestedInput = {
  create?: Prisma.XOR<Prisma.OrderCreateWithoutStocksInput, Prisma.OrderUncheckedCreateWithoutStocksInput>
  connectOrCreate?: Prisma.OrderCreateOrConnectWithoutStocksInput
  upsert?: Prisma.OrderUpsertWithoutStocksInput
  disconnect?: Prisma.OrderWhereInput | boolean
  delete?: Prisma.OrderWhereInput | boolean
  connect?: Prisma.OrderWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.OrderUpdateToOneWithWhereWithoutStocksInput, Prisma.OrderUpdateWithoutStocksInput>, Prisma.OrderUncheckedUpdateWithoutStocksInput>
}

export type OrderCreateWithoutVariantInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Date | string
  updatedAt?: Date | string
  stocks?: Prisma.StockCreateNestedManyWithoutOrderInput
}

export type OrderUncheckedCreateWithoutVariantInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Date | string
  updatedAt?: Date | string
  stocks?: Prisma.StockUncheckedCreateNestedManyWithoutOrderInput
}

export type OrderCreateOrConnectWithoutVariantInput = {
  where: Prisma.OrderWhereUniqueInput
  create: Prisma.XOR<Prisma.OrderCreateWithoutVariantInput, Prisma.OrderUncheckedCreateWithoutVariantInput>
}

export type OrderCreateManyVariantInputEnvelope = {
  data: Prisma.OrderCreateManyVariantInput | Prisma.OrderCreateManyVariantInput[]
  skipDuplicates?: boolean
}

export type OrderUpsertWithWhereUniqueWithoutVariantInput = {
  where: Prisma.OrderWhereUniqueInput
  update: Prisma.XOR<Prisma.OrderUpdateWithoutVariantInput, Prisma.OrderUncheckedUpdateWithoutVariantInput>
  create: Prisma.XOR<Prisma.OrderCreateWithoutVariantInput, Prisma.OrderUncheckedCreateWithoutVariantInput>
}

export type OrderUpdateWithWhereUniqueWithoutVariantInput = {
  where: Prisma.OrderWhereUniqueInput
  data: Prisma.XOR<Prisma.OrderUpdateWithoutVariantInput, Prisma.OrderUncheckedUpdateWithoutVariantInput>
}

export type OrderUpdateManyWithWhereWithoutVariantInput = {
  where: Prisma.OrderScalarWhereInput
  data: Prisma.XOR<Prisma.OrderUpdateManyMutationInput, Prisma.OrderUncheckedUpdateManyWithoutVariantInput>
}

export type OrderScalarWhereInput = {
  AND?: Prisma.OrderScalarWhereInput | Prisma.OrderScalarWhereInput[]
  OR?: Prisma.OrderScalarWhereInput[]
  NOT?: Prisma.OrderScalarWhereInput | Prisma.OrderScalarWhereInput[]
  id?: Prisma.StringFilter<"Order"> | string
  invoiceNumber?: Prisma.StringFilter<"Order"> | string
  quantity?: Prisma.IntFilter<"Order"> | number
  status?: Prisma.EnumOrderStatusFilter<"Order"> | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFilter<"Order"> | string
  buyerWhatsapp?: Prisma.StringFilter<"Order"> | string
  deliveredCredentials?: Prisma.JsonNullableListFilter<"Order">
  variantId?: Prisma.StringFilter<"Order"> | string
  createdAt?: Prisma.DateTimeFilter<"Order"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Order"> | Date | string
}

export type OrderCreateWithoutStocksInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Date | string
  updatedAt?: Date | string
  variant: Prisma.VariantCreateNestedOneWithoutOrdersInput
}

export type OrderUncheckedCreateWithoutStocksInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  variantId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type OrderCreateOrConnectWithoutStocksInput = {
  where: Prisma.OrderWhereUniqueInput
  create: Prisma.XOR<Prisma.OrderCreateWithoutStocksInput, Prisma.OrderUncheckedCreateWithoutStocksInput>
}

export type OrderUpsertWithoutStocksInput = {
  update: Prisma.XOR<Prisma.OrderUpdateWithoutStocksInput, Prisma.OrderUncheckedUpdateWithoutStocksInput>
  create: Prisma.XOR<Prisma.OrderCreateWithoutStocksInput, Prisma.OrderUncheckedCreateWithoutStocksInput>
  where?: Prisma.OrderWhereInput
}

export type OrderUpdateToOneWithWhereWithoutStocksInput = {
  where?: Prisma.OrderWhereInput
  data: Prisma.XOR<Prisma.OrderUpdateWithoutStocksInput, Prisma.OrderUncheckedUpdateWithoutStocksInput>
}

export type OrderUpdateWithoutStocksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  variant?: Prisma.VariantUpdateOneRequiredWithoutOrdersNestedInput
}

export type OrderUncheckedUpdateWithoutStocksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  variantId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type OrderCreateManyVariantInput = {
  id?: string
  invoiceNumber: string
  quantity?: number
  status?: $Enums.OrderStatus
  buyerEmail: string
  buyerWhatsapp: string
  deliveredCredentials?: Prisma.OrderCreatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type OrderUpdateWithoutVariantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  stocks?: Prisma.StockUpdateManyWithoutOrderNestedInput
}

export type OrderUncheckedUpdateWithoutVariantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  stocks?: Prisma.StockUncheckedUpdateManyWithoutOrderNestedInput
}

export type OrderUncheckedUpdateManyWithoutVariantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  quantity?: Prisma.IntFieldUpdateOperationsInput | number
  status?: Prisma.EnumOrderStatusFieldUpdateOperationsInput | $Enums.OrderStatus
  buyerEmail?: Prisma.StringFieldUpdateOperationsInput | string
  buyerWhatsapp?: Prisma.StringFieldUpdateOperationsInput | string
  deliveredCredentials?: Prisma.OrderUpdatedeliveredCredentialsInput | runtime.InputJsonValue[]
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type OrderCountOutputType
 */

export type OrderCountOutputType = {
  stocks: number
}

export type OrderCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  stocks?: boolean | OrderCountOutputTypeCountStocksArgs
}

/**
 * OrderCountOutputType without action
 */
export type OrderCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the OrderCountOutputType
   */
  select?: Prisma.OrderCountOutputTypeSelect<ExtArgs> | null
}

/**
 * OrderCountOutputType without action
 */
export type OrderCountOutputTypeCountStocksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.StockWhereInput
}


export type OrderSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  invoiceNumber?: boolean
  quantity?: boolean
  status?: boolean
  buyerEmail?: boolean
  buyerWhatsapp?: boolean
  deliveredCredentials?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
  stocks?: boolean | Prisma.Order$stocksArgs<ExtArgs>
  _count?: boolean | Prisma.OrderCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["order"]>

export type OrderSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  invoiceNumber?: boolean
  quantity?: boolean
  status?: boolean
  buyerEmail?: boolean
  buyerWhatsapp?: boolean
  deliveredCredentials?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["order"]>

export type OrderSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  invoiceNumber?: boolean
  quantity?: boolean
  status?: boolean
  buyerEmail?: boolean
  buyerWhatsapp?: boolean
  deliveredCredentials?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["order"]>

export type OrderSelectScalar = {
  id?: boolean
  invoiceNumber?: boolean
  quantity?: boolean
  status?: boolean
  buyerEmail?: boolean
  buyerWhatsapp?: boolean
  deliveredCredentials?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type OrderOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "invoiceNumber" | "quantity" | "status" | "buyerEmail" | "buyerWhatsapp" | "deliveredCredentials" | "variantId" | "createdAt" | "updatedAt", ExtArgs["result"]["order"]>
export type OrderInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
  stocks?: boolean | Prisma.Order$stocksArgs<ExtArgs>
  _count?: boolean | Prisma.OrderCountOutputTypeDefaultArgs<ExtArgs>
}
export type OrderIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}
export type OrderIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}

export type $OrderPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Order"
  objects: {
    variant: Prisma.$VariantPayload<ExtArgs>
    stocks: Prisma.$StockPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    invoiceNumber: string
    quantity: number
    status: $Enums.OrderStatus
    buyerEmail: string
    buyerWhatsapp: string
    deliveredCredentials: runtime.JsonValue[]
    variantId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["order"]>
  composites: {}
}

export type OrderGetPayload<S extends boolean | null | undefined | OrderDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$OrderPayload, S>

export type OrderCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<OrderFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: OrderCountAggregateInputType | true
  }

export interface OrderDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Order'], meta: { name: 'Order' } }
  /**
   * Find zero or one Order that matches the filter.
   * @param {OrderFindUniqueArgs} args - Arguments to find a Order
   * @example
   * // Get one Order
   * const order = await prisma.order.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends OrderFindUniqueArgs>(args: Prisma.SelectSubset<T, OrderFindUniqueArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Order that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {OrderFindUniqueOrThrowArgs} args - Arguments to find a Order
   * @example
   * // Get one Order
   * const order = await prisma.order.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends OrderFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, OrderFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Order that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrderFindFirstArgs} args - Arguments to find a Order
   * @example
   * // Get one Order
   * const order = await prisma.order.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends OrderFindFirstArgs>(args?: Prisma.SelectSubset<T, OrderFindFirstArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Order that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrderFindFirstOrThrowArgs} args - Arguments to find a Order
   * @example
   * // Get one Order
   * const order = await prisma.order.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends OrderFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, OrderFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Orders that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrderFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Orders
   * const orders = await prisma.order.findMany()
   * 
   * // Get first 10 Orders
   * const orders = await prisma.order.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const orderWithIdOnly = await prisma.order.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends OrderFindManyArgs>(args?: Prisma.SelectSubset<T, OrderFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Order.
   * @param {OrderCreateArgs} args - Arguments to create a Order.
   * @example
   * // Create one Order
   * const Order = await prisma.order.create({
   *   data: {
   *     // ... data to create a Order
   *   }
   * })
   * 
   */
  create<T extends OrderCreateArgs>(args: Prisma.SelectSubset<T, OrderCreateArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Orders.
   * @param {OrderCreateManyArgs} args - Arguments to create many Orders.
   * @example
   * // Create many Orders
   * const order = await prisma.order.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends OrderCreateManyArgs>(args?: Prisma.SelectSubset<T, OrderCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Orders and returns the data saved in the database.
   * @param {OrderCreateManyAndReturnArgs} args - Arguments to create many Orders.
   * @example
   * // Create many Orders
   * const order = await prisma.order.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Orders and only return the `id`
   * const orderWithIdOnly = await prisma.order.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends OrderCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, OrderCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Order.
   * @param {OrderDeleteArgs} args - Arguments to delete one Order.
   * @example
   * // Delete one Order
   * const Order = await prisma.order.delete({
   *   where: {
   *     // ... filter to delete one Order
   *   }
   * })
   * 
   */
  delete<T extends OrderDeleteArgs>(args: Prisma.SelectSubset<T, OrderDeleteArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Order.
   * @param {OrderUpdateArgs} args - Arguments to update one Order.
   * @example
   * // Update one Order
   * const order = await prisma.order.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends OrderUpdateArgs>(args: Prisma.SelectSubset<T, OrderUpdateArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Orders.
   * @param {OrderDeleteManyArgs} args - Arguments to filter Orders to delete.
   * @example
   * // Delete a few Orders
   * const { count } = await prisma.order.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends OrderDeleteManyArgs>(args?: Prisma.SelectSubset<T, OrderDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Orders.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrderUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Orders
   * const order = await prisma.order.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends OrderUpdateManyArgs>(args: Prisma.SelectSubset<T, OrderUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Orders and returns the data updated in the database.
   * @param {OrderUpdateManyAndReturnArgs} args - Arguments to update many Orders.
   * @example
   * // Update many Orders
   * const order = await prisma.order.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Orders and only return the `id`
   * const orderWithIdOnly = await prisma.order.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends OrderUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, OrderUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Order.
   * @param {OrderUpsertArgs} args - Arguments to update or create a Order.
   * @example
   * // Update or create a Order
   * const order = await prisma.order.upsert({
   *   create: {
   *     // ... data to create a Order
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Order we want to update
   *   }
   * })
   */
  upsert<T extends OrderUpsertArgs>(args: Prisma.SelectSubset<T, OrderUpsertArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Orders.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrderCountArgs} args - Arguments to filter Orders to count.
   * @example
   * // Count the number of Orders
   * const count = await prisma.order.count({
   *   where: {
   *     // ... the filter for the Orders we want to count
   *   }
   * })
  **/
  count<T extends OrderCountArgs>(
    args?: Prisma.Subset<T, OrderCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], OrderCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Order.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrderAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends OrderAggregateArgs>(args: Prisma.Subset<T, OrderAggregateArgs>): Prisma.PrismaPromise<GetOrderAggregateType<T>>

  /**
   * Group by Order.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {OrderGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends OrderGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: OrderGroupByArgs['orderBy'] }
      : { orderBy?: OrderGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, OrderGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetOrderGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Order model
 */
readonly fields: OrderFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Order.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__OrderClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  variant<T extends Prisma.VariantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.VariantDefaultArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  stocks<T extends Prisma.Order$stocksArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Order$stocksArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Order model
 */
export interface OrderFieldRefs {
  readonly id: Prisma.FieldRef<"Order", 'String'>
  readonly invoiceNumber: Prisma.FieldRef<"Order", 'String'>
  readonly quantity: Prisma.FieldRef<"Order", 'Int'>
  readonly status: Prisma.FieldRef<"Order", 'OrderStatus'>
  readonly buyerEmail: Prisma.FieldRef<"Order", 'String'>
  readonly buyerWhatsapp: Prisma.FieldRef<"Order", 'String'>
  readonly deliveredCredentials: Prisma.FieldRef<"Order", 'Json[]'>
  readonly variantId: Prisma.FieldRef<"Order", 'String'>
  readonly createdAt: Prisma.FieldRef<"Order", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Order", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Order findUnique
 */
export type OrderFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * Filter, which Order to fetch.
   */
  where: Prisma.OrderWhereUniqueInput
}

/**
 * Order findUniqueOrThrow
 */
export type OrderFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * Filter, which Order to fetch.
   */
  where: Prisma.OrderWhereUniqueInput
}

/**
 * Order findFirst
 */
export type OrderFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * Filter, which Order to fetch.
   */
  where?: Prisma.OrderWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Orders to fetch.
   */
  orderBy?: Prisma.OrderOrderByWithRelationInput | Prisma.OrderOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Orders.
   */
  cursor?: Prisma.OrderWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Orders from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Orders.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Orders.
   */
  distinct?: Prisma.OrderScalarFieldEnum | Prisma.OrderScalarFieldEnum[]
}

/**
 * Order findFirstOrThrow
 */
export type OrderFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * Filter, which Order to fetch.
   */
  where?: Prisma.OrderWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Orders to fetch.
   */
  orderBy?: Prisma.OrderOrderByWithRelationInput | Prisma.OrderOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Orders.
   */
  cursor?: Prisma.OrderWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Orders from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Orders.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Orders.
   */
  distinct?: Prisma.OrderScalarFieldEnum | Prisma.OrderScalarFieldEnum[]
}

/**
 * Order findMany
 */
export type OrderFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * Filter, which Orders to fetch.
   */
  where?: Prisma.OrderWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Orders to fetch.
   */
  orderBy?: Prisma.OrderOrderByWithRelationInput | Prisma.OrderOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Orders.
   */
  cursor?: Prisma.OrderWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Orders from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Orders.
   */
  skip?: number
  distinct?: Prisma.OrderScalarFieldEnum | Prisma.OrderScalarFieldEnum[]
}

/**
 * Order create
 */
export type OrderCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * The data needed to create a Order.
   */
  data: Prisma.XOR<Prisma.OrderCreateInput, Prisma.OrderUncheckedCreateInput>
}

/**
 * Order createMany
 */
export type OrderCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Orders.
   */
  data: Prisma.OrderCreateManyInput | Prisma.OrderCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Order createManyAndReturn
 */
export type OrderCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * The data used to create many Orders.
   */
  data: Prisma.OrderCreateManyInput | Prisma.OrderCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Order update
 */
export type OrderUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * The data needed to update a Order.
   */
  data: Prisma.XOR<Prisma.OrderUpdateInput, Prisma.OrderUncheckedUpdateInput>
  /**
   * Choose, which Order to update.
   */
  where: Prisma.OrderWhereUniqueInput
}

/**
 * Order updateMany
 */
export type OrderUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Orders.
   */
  data: Prisma.XOR<Prisma.OrderUpdateManyMutationInput, Prisma.OrderUncheckedUpdateManyInput>
  /**
   * Filter which Orders to update
   */
  where?: Prisma.OrderWhereInput
  /**
   * Limit how many Orders to update.
   */
  limit?: number
}

/**
 * Order updateManyAndReturn
 */
export type OrderUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * The data used to update Orders.
   */
  data: Prisma.XOR<Prisma.OrderUpdateManyMutationInput, Prisma.OrderUncheckedUpdateManyInput>
  /**
   * Filter which Orders to update
   */
  where?: Prisma.OrderWhereInput
  /**
   * Limit how many Orders to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Order upsert
 */
export type OrderUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * The filter to search for the Order to update in case it exists.
   */
  where: Prisma.OrderWhereUniqueInput
  /**
   * In case the Order found by the `where` argument doesn't exist, create a new Order with this data.
   */
  create: Prisma.XOR<Prisma.OrderCreateInput, Prisma.OrderUncheckedCreateInput>
  /**
   * In case the Order was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.OrderUpdateInput, Prisma.OrderUncheckedUpdateInput>
}

/**
 * Order delete
 */
export type OrderDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  /**
   * Filter which Order to delete.
   */
  where: Prisma.OrderWhereUniqueInput
}

/**
 * Order deleteMany
 */
export type OrderDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Orders to delete
   */
  where?: Prisma.OrderWhereInput
  /**
   * Limit how many Orders to delete.
   */
  limit?: number
}

/**
 * Order.stocks
 */
export type Order$stocksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  where?: Prisma.StockWhereInput
  orderBy?: Prisma.StockOrderByWithRelationInput | Prisma.StockOrderByWithRelationInput[]
  cursor?: Prisma.StockWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.StockScalarFieldEnum | Prisma.StockScalarFieldEnum[]
}

/**
 * Order without action
 */
export type OrderDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
}
