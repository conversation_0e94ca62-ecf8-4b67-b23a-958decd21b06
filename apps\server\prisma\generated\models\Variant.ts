
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Variant` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Variant
 * 
 */
export type VariantModel = runtime.Types.Result.DefaultSelection<Prisma.$VariantPayload>

export type AggregateVariant = {
  _count: VariantCountAggregateOutputType | null
  _avg: VariantAvgAggregateOutputType | null
  _sum: VariantSumAggregateOutputType | null
  _min: VariantMinAggregateOutputType | null
  _max: VariantMaxAggregateOutputType | null
}

export type VariantAvgAggregateOutputType = {
  price: runtime.Decimal | null
}

export type VariantSumAggregateOutputType = {
  price: runtime.Decimal | null
}

export type VariantMinAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  price: runtime.Decimal | null
  productId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type VariantMaxAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  price: runtime.Decimal | null
  productId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type VariantCountAggregateOutputType = {
  id: number
  name: number
  description: number
  price: number
  productId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type VariantAvgAggregateInputType = {
  price?: true
}

export type VariantSumAggregateInputType = {
  price?: true
}

export type VariantMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  price?: true
  productId?: true
  createdAt?: true
  updatedAt?: true
}

export type VariantMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  price?: true
  productId?: true
  createdAt?: true
  updatedAt?: true
}

export type VariantCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  price?: true
  productId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type VariantAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Variant to aggregate.
   */
  where?: Prisma.VariantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Variants to fetch.
   */
  orderBy?: Prisma.VariantOrderByWithRelationInput | Prisma.VariantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.VariantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Variants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Variants.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Variants
  **/
  _count?: true | VariantCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: VariantAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: VariantSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: VariantMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: VariantMaxAggregateInputType
}

export type GetVariantAggregateType<T extends VariantAggregateArgs> = {
      [P in keyof T & keyof AggregateVariant]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateVariant[P]>
    : Prisma.GetScalarType<T[P], AggregateVariant[P]>
}




export type VariantGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.VariantWhereInput
  orderBy?: Prisma.VariantOrderByWithAggregationInput | Prisma.VariantOrderByWithAggregationInput[]
  by: Prisma.VariantScalarFieldEnum[] | Prisma.VariantScalarFieldEnum
  having?: Prisma.VariantScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: VariantCountAggregateInputType | true
  _avg?: VariantAvgAggregateInputType
  _sum?: VariantSumAggregateInputType
  _min?: VariantMinAggregateInputType
  _max?: VariantMaxAggregateInputType
}

export type VariantGroupByOutputType = {
  id: string
  name: string
  description: string | null
  price: runtime.Decimal
  productId: string
  createdAt: Date
  updatedAt: Date
  _count: VariantCountAggregateOutputType | null
  _avg: VariantAvgAggregateOutputType | null
  _sum: VariantSumAggregateOutputType | null
  _min: VariantMinAggregateOutputType | null
  _max: VariantMaxAggregateOutputType | null
}

type GetVariantGroupByPayload<T extends VariantGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<VariantGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof VariantGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], VariantGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], VariantGroupByOutputType[P]>
      }
    >
  > 



export type VariantWhereInput = {
  AND?: Prisma.VariantWhereInput | Prisma.VariantWhereInput[]
  OR?: Prisma.VariantWhereInput[]
  NOT?: Prisma.VariantWhereInput | Prisma.VariantWhereInput[]
  id?: Prisma.StringFilter<"Variant"> | string
  name?: Prisma.StringFilter<"Variant"> | string
  description?: Prisma.StringNullableFilter<"Variant"> | string | null
  price?: Prisma.DecimalFilter<"Variant"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringFilter<"Variant"> | string
  createdAt?: Prisma.DateTimeFilter<"Variant"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Variant"> | Date | string
  product?: Prisma.XOR<Prisma.ProductScalarRelationFilter, Prisma.ProductWhereInput>
  orders?: Prisma.OrderListRelationFilter
  stock?: Prisma.StockListRelationFilter
}

export type VariantOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  price?: Prisma.SortOrder
  productId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  product?: Prisma.ProductOrderByWithRelationInput
  orders?: Prisma.OrderOrderByRelationAggregateInput
  stock?: Prisma.StockOrderByRelationAggregateInput
}

export type VariantWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.VariantWhereInput | Prisma.VariantWhereInput[]
  OR?: Prisma.VariantWhereInput[]
  NOT?: Prisma.VariantWhereInput | Prisma.VariantWhereInput[]
  name?: Prisma.StringFilter<"Variant"> | string
  description?: Prisma.StringNullableFilter<"Variant"> | string | null
  price?: Prisma.DecimalFilter<"Variant"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringFilter<"Variant"> | string
  createdAt?: Prisma.DateTimeFilter<"Variant"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Variant"> | Date | string
  product?: Prisma.XOR<Prisma.ProductScalarRelationFilter, Prisma.ProductWhereInput>
  orders?: Prisma.OrderListRelationFilter
  stock?: Prisma.StockListRelationFilter
}, "id">

export type VariantOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  price?: Prisma.SortOrder
  productId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.VariantCountOrderByAggregateInput
  _avg?: Prisma.VariantAvgOrderByAggregateInput
  _max?: Prisma.VariantMaxOrderByAggregateInput
  _min?: Prisma.VariantMinOrderByAggregateInput
  _sum?: Prisma.VariantSumOrderByAggregateInput
}

export type VariantScalarWhereWithAggregatesInput = {
  AND?: Prisma.VariantScalarWhereWithAggregatesInput | Prisma.VariantScalarWhereWithAggregatesInput[]
  OR?: Prisma.VariantScalarWhereWithAggregatesInput[]
  NOT?: Prisma.VariantScalarWhereWithAggregatesInput | Prisma.VariantScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Variant"> | string
  name?: Prisma.StringWithAggregatesFilter<"Variant"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"Variant"> | string | null
  price?: Prisma.DecimalWithAggregatesFilter<"Variant"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringWithAggregatesFilter<"Variant"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Variant"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Variant"> | Date | string
}

export type VariantCreateInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt: Date | string
  updatedAt: Date | string
  product: Prisma.ProductCreateNestedOneWithoutVariantsInput
  orders?: Prisma.OrderCreateNestedManyWithoutVariantInput
  stock?: Prisma.StockCreateNestedManyWithoutVariantInput
}

export type VariantUncheckedCreateInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  productId: string
  createdAt: Date | string
  updatedAt: Date | string
  orders?: Prisma.OrderUncheckedCreateNestedManyWithoutVariantInput
  stock?: Prisma.StockUncheckedCreateNestedManyWithoutVariantInput
}

export type VariantUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  product?: Prisma.ProductUpdateOneRequiredWithoutVariantsNestedInput
  orders?: Prisma.OrderUpdateManyWithoutVariantNestedInput
  stock?: Prisma.StockUpdateManyWithoutVariantNestedInput
}

export type VariantUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  orders?: Prisma.OrderUncheckedUpdateManyWithoutVariantNestedInput
  stock?: Prisma.StockUncheckedUpdateManyWithoutVariantNestedInput
}

export type VariantCreateManyInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  productId: string
  createdAt: Date | string
  updatedAt: Date | string
}

export type VariantUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type VariantUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type VariantScalarRelationFilter = {
  is?: Prisma.VariantWhereInput
  isNot?: Prisma.VariantWhereInput
}

export type VariantListRelationFilter = {
  every?: Prisma.VariantWhereInput
  some?: Prisma.VariantWhereInput
  none?: Prisma.VariantWhereInput
}

export type VariantOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type VariantCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  price?: Prisma.SortOrder
  productId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type VariantAvgOrderByAggregateInput = {
  price?: Prisma.SortOrder
}

export type VariantMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  price?: Prisma.SortOrder
  productId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type VariantMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  price?: Prisma.SortOrder
  productId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type VariantSumOrderByAggregateInput = {
  price?: Prisma.SortOrder
}

export type VariantCreateNestedOneWithoutOrdersInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutOrdersInput, Prisma.VariantUncheckedCreateWithoutOrdersInput>
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutOrdersInput
  connect?: Prisma.VariantWhereUniqueInput
}

export type VariantUpdateOneRequiredWithoutOrdersNestedInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutOrdersInput, Prisma.VariantUncheckedCreateWithoutOrdersInput>
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutOrdersInput
  upsert?: Prisma.VariantUpsertWithoutOrdersInput
  connect?: Prisma.VariantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.VariantUpdateToOneWithWhereWithoutOrdersInput, Prisma.VariantUpdateWithoutOrdersInput>, Prisma.VariantUncheckedUpdateWithoutOrdersInput>
}

export type VariantCreateNestedManyWithoutProductInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutProductInput, Prisma.VariantUncheckedCreateWithoutProductInput> | Prisma.VariantCreateWithoutProductInput[] | Prisma.VariantUncheckedCreateWithoutProductInput[]
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutProductInput | Prisma.VariantCreateOrConnectWithoutProductInput[]
  createMany?: Prisma.VariantCreateManyProductInputEnvelope
  connect?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
}

export type VariantUncheckedCreateNestedManyWithoutProductInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutProductInput, Prisma.VariantUncheckedCreateWithoutProductInput> | Prisma.VariantCreateWithoutProductInput[] | Prisma.VariantUncheckedCreateWithoutProductInput[]
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutProductInput | Prisma.VariantCreateOrConnectWithoutProductInput[]
  createMany?: Prisma.VariantCreateManyProductInputEnvelope
  connect?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
}

export type VariantUpdateManyWithoutProductNestedInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutProductInput, Prisma.VariantUncheckedCreateWithoutProductInput> | Prisma.VariantCreateWithoutProductInput[] | Prisma.VariantUncheckedCreateWithoutProductInput[]
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutProductInput | Prisma.VariantCreateOrConnectWithoutProductInput[]
  upsert?: Prisma.VariantUpsertWithWhereUniqueWithoutProductInput | Prisma.VariantUpsertWithWhereUniqueWithoutProductInput[]
  createMany?: Prisma.VariantCreateManyProductInputEnvelope
  set?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  disconnect?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  delete?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  connect?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  update?: Prisma.VariantUpdateWithWhereUniqueWithoutProductInput | Prisma.VariantUpdateWithWhereUniqueWithoutProductInput[]
  updateMany?: Prisma.VariantUpdateManyWithWhereWithoutProductInput | Prisma.VariantUpdateManyWithWhereWithoutProductInput[]
  deleteMany?: Prisma.VariantScalarWhereInput | Prisma.VariantScalarWhereInput[]
}

export type VariantUncheckedUpdateManyWithoutProductNestedInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutProductInput, Prisma.VariantUncheckedCreateWithoutProductInput> | Prisma.VariantCreateWithoutProductInput[] | Prisma.VariantUncheckedCreateWithoutProductInput[]
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutProductInput | Prisma.VariantCreateOrConnectWithoutProductInput[]
  upsert?: Prisma.VariantUpsertWithWhereUniqueWithoutProductInput | Prisma.VariantUpsertWithWhereUniqueWithoutProductInput[]
  createMany?: Prisma.VariantCreateManyProductInputEnvelope
  set?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  disconnect?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  delete?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  connect?: Prisma.VariantWhereUniqueInput | Prisma.VariantWhereUniqueInput[]
  update?: Prisma.VariantUpdateWithWhereUniqueWithoutProductInput | Prisma.VariantUpdateWithWhereUniqueWithoutProductInput[]
  updateMany?: Prisma.VariantUpdateManyWithWhereWithoutProductInput | Prisma.VariantUpdateManyWithWhereWithoutProductInput[]
  deleteMany?: Prisma.VariantScalarWhereInput | Prisma.VariantScalarWhereInput[]
}

export type DecimalFieldUpdateOperationsInput = {
  set?: runtime.Decimal | runtime.DecimalJsLike | number | string
  increment?: runtime.Decimal | runtime.DecimalJsLike | number | string
  decrement?: runtime.Decimal | runtime.DecimalJsLike | number | string
  multiply?: runtime.Decimal | runtime.DecimalJsLike | number | string
  divide?: runtime.Decimal | runtime.DecimalJsLike | number | string
}

export type VariantCreateNestedOneWithoutStockInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutStockInput, Prisma.VariantUncheckedCreateWithoutStockInput>
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutStockInput
  connect?: Prisma.VariantWhereUniqueInput
}

export type VariantUpdateOneRequiredWithoutStockNestedInput = {
  create?: Prisma.XOR<Prisma.VariantCreateWithoutStockInput, Prisma.VariantUncheckedCreateWithoutStockInput>
  connectOrCreate?: Prisma.VariantCreateOrConnectWithoutStockInput
  upsert?: Prisma.VariantUpsertWithoutStockInput
  connect?: Prisma.VariantWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.VariantUpdateToOneWithWhereWithoutStockInput, Prisma.VariantUpdateWithoutStockInput>, Prisma.VariantUncheckedUpdateWithoutStockInput>
}

export type VariantCreateWithoutOrdersInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt: Date | string
  updatedAt: Date | string
  product: Prisma.ProductCreateNestedOneWithoutVariantsInput
  stock?: Prisma.StockCreateNestedManyWithoutVariantInput
}

export type VariantUncheckedCreateWithoutOrdersInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  productId: string
  createdAt: Date | string
  updatedAt: Date | string
  stock?: Prisma.StockUncheckedCreateNestedManyWithoutVariantInput
}

export type VariantCreateOrConnectWithoutOrdersInput = {
  where: Prisma.VariantWhereUniqueInput
  create: Prisma.XOR<Prisma.VariantCreateWithoutOrdersInput, Prisma.VariantUncheckedCreateWithoutOrdersInput>
}

export type VariantUpsertWithoutOrdersInput = {
  update: Prisma.XOR<Prisma.VariantUpdateWithoutOrdersInput, Prisma.VariantUncheckedUpdateWithoutOrdersInput>
  create: Prisma.XOR<Prisma.VariantCreateWithoutOrdersInput, Prisma.VariantUncheckedCreateWithoutOrdersInput>
  where?: Prisma.VariantWhereInput
}

export type VariantUpdateToOneWithWhereWithoutOrdersInput = {
  where?: Prisma.VariantWhereInput
  data: Prisma.XOR<Prisma.VariantUpdateWithoutOrdersInput, Prisma.VariantUncheckedUpdateWithoutOrdersInput>
}

export type VariantUpdateWithoutOrdersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  product?: Prisma.ProductUpdateOneRequiredWithoutVariantsNestedInput
  stock?: Prisma.StockUpdateManyWithoutVariantNestedInput
}

export type VariantUncheckedUpdateWithoutOrdersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  stock?: Prisma.StockUncheckedUpdateManyWithoutVariantNestedInput
}

export type VariantCreateWithoutProductInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt: Date | string
  updatedAt: Date | string
  orders?: Prisma.OrderCreateNestedManyWithoutVariantInput
  stock?: Prisma.StockCreateNestedManyWithoutVariantInput
}

export type VariantUncheckedCreateWithoutProductInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt: Date | string
  updatedAt: Date | string
  orders?: Prisma.OrderUncheckedCreateNestedManyWithoutVariantInput
  stock?: Prisma.StockUncheckedCreateNestedManyWithoutVariantInput
}

export type VariantCreateOrConnectWithoutProductInput = {
  where: Prisma.VariantWhereUniqueInput
  create: Prisma.XOR<Prisma.VariantCreateWithoutProductInput, Prisma.VariantUncheckedCreateWithoutProductInput>
}

export type VariantCreateManyProductInputEnvelope = {
  data: Prisma.VariantCreateManyProductInput | Prisma.VariantCreateManyProductInput[]
  skipDuplicates?: boolean
}

export type VariantUpsertWithWhereUniqueWithoutProductInput = {
  where: Prisma.VariantWhereUniqueInput
  update: Prisma.XOR<Prisma.VariantUpdateWithoutProductInput, Prisma.VariantUncheckedUpdateWithoutProductInput>
  create: Prisma.XOR<Prisma.VariantCreateWithoutProductInput, Prisma.VariantUncheckedCreateWithoutProductInput>
}

export type VariantUpdateWithWhereUniqueWithoutProductInput = {
  where: Prisma.VariantWhereUniqueInput
  data: Prisma.XOR<Prisma.VariantUpdateWithoutProductInput, Prisma.VariantUncheckedUpdateWithoutProductInput>
}

export type VariantUpdateManyWithWhereWithoutProductInput = {
  where: Prisma.VariantScalarWhereInput
  data: Prisma.XOR<Prisma.VariantUpdateManyMutationInput, Prisma.VariantUncheckedUpdateManyWithoutProductInput>
}

export type VariantScalarWhereInput = {
  AND?: Prisma.VariantScalarWhereInput | Prisma.VariantScalarWhereInput[]
  OR?: Prisma.VariantScalarWhereInput[]
  NOT?: Prisma.VariantScalarWhereInput | Prisma.VariantScalarWhereInput[]
  id?: Prisma.StringFilter<"Variant"> | string
  name?: Prisma.StringFilter<"Variant"> | string
  description?: Prisma.StringNullableFilter<"Variant"> | string | null
  price?: Prisma.DecimalFilter<"Variant"> | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringFilter<"Variant"> | string
  createdAt?: Prisma.DateTimeFilter<"Variant"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Variant"> | Date | string
}

export type VariantCreateWithoutStockInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt: Date | string
  updatedAt: Date | string
  product: Prisma.ProductCreateNestedOneWithoutVariantsInput
  orders?: Prisma.OrderCreateNestedManyWithoutVariantInput
}

export type VariantUncheckedCreateWithoutStockInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  productId: string
  createdAt: Date | string
  updatedAt: Date | string
  orders?: Prisma.OrderUncheckedCreateNestedManyWithoutVariantInput
}

export type VariantCreateOrConnectWithoutStockInput = {
  where: Prisma.VariantWhereUniqueInput
  create: Prisma.XOR<Prisma.VariantCreateWithoutStockInput, Prisma.VariantUncheckedCreateWithoutStockInput>
}

export type VariantUpsertWithoutStockInput = {
  update: Prisma.XOR<Prisma.VariantUpdateWithoutStockInput, Prisma.VariantUncheckedUpdateWithoutStockInput>
  create: Prisma.XOR<Prisma.VariantCreateWithoutStockInput, Prisma.VariantUncheckedCreateWithoutStockInput>
  where?: Prisma.VariantWhereInput
}

export type VariantUpdateToOneWithWhereWithoutStockInput = {
  where?: Prisma.VariantWhereInput
  data: Prisma.XOR<Prisma.VariantUpdateWithoutStockInput, Prisma.VariantUncheckedUpdateWithoutStockInput>
}

export type VariantUpdateWithoutStockInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  product?: Prisma.ProductUpdateOneRequiredWithoutVariantsNestedInput
  orders?: Prisma.OrderUpdateManyWithoutVariantNestedInput
}

export type VariantUncheckedUpdateWithoutStockInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  productId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  orders?: Prisma.OrderUncheckedUpdateManyWithoutVariantNestedInput
}

export type VariantCreateManyProductInput = {
  id?: string
  name: string
  description?: string | null
  price: runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt: Date | string
  updatedAt: Date | string
}

export type VariantUpdateWithoutProductInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  orders?: Prisma.OrderUpdateManyWithoutVariantNestedInput
  stock?: Prisma.StockUpdateManyWithoutVariantNestedInput
}

export type VariantUncheckedUpdateWithoutProductInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  orders?: Prisma.OrderUncheckedUpdateManyWithoutVariantNestedInput
  stock?: Prisma.StockUncheckedUpdateManyWithoutVariantNestedInput
}

export type VariantUncheckedUpdateManyWithoutProductInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  price?: Prisma.DecimalFieldUpdateOperationsInput | runtime.Decimal | runtime.DecimalJsLike | number | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type VariantCountOutputType
 */

export type VariantCountOutputType = {
  orders: number
  stock: number
}

export type VariantCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  orders?: boolean | VariantCountOutputTypeCountOrdersArgs
  stock?: boolean | VariantCountOutputTypeCountStockArgs
}

/**
 * VariantCountOutputType without action
 */
export type VariantCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the VariantCountOutputType
   */
  select?: Prisma.VariantCountOutputTypeSelect<ExtArgs> | null
}

/**
 * VariantCountOutputType without action
 */
export type VariantCountOutputTypeCountOrdersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.OrderWhereInput
}

/**
 * VariantCountOutputType without action
 */
export type VariantCountOutputTypeCountStockArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.StockWhereInput
}


export type VariantSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  price?: boolean
  productId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  product?: boolean | Prisma.ProductDefaultArgs<ExtArgs>
  orders?: boolean | Prisma.Variant$ordersArgs<ExtArgs>
  stock?: boolean | Prisma.Variant$stockArgs<ExtArgs>
  _count?: boolean | Prisma.VariantCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["variant"]>

export type VariantSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  price?: boolean
  productId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  product?: boolean | Prisma.ProductDefaultArgs<ExtArgs>
}, ExtArgs["result"]["variant"]>

export type VariantSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  price?: boolean
  productId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  product?: boolean | Prisma.ProductDefaultArgs<ExtArgs>
}, ExtArgs["result"]["variant"]>

export type VariantSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  price?: boolean
  productId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type VariantOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "description" | "price" | "productId" | "createdAt" | "updatedAt", ExtArgs["result"]["variant"]>
export type VariantInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  product?: boolean | Prisma.ProductDefaultArgs<ExtArgs>
  orders?: boolean | Prisma.Variant$ordersArgs<ExtArgs>
  stock?: boolean | Prisma.Variant$stockArgs<ExtArgs>
  _count?: boolean | Prisma.VariantCountOutputTypeDefaultArgs<ExtArgs>
}
export type VariantIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  product?: boolean | Prisma.ProductDefaultArgs<ExtArgs>
}
export type VariantIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  product?: boolean | Prisma.ProductDefaultArgs<ExtArgs>
}

export type $VariantPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Variant"
  objects: {
    product: Prisma.$ProductPayload<ExtArgs>
    orders: Prisma.$OrderPayload<ExtArgs>[]
    stock: Prisma.$StockPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    description: string | null
    price: runtime.Decimal
    productId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["variant"]>
  composites: {}
}

export type VariantGetPayload<S extends boolean | null | undefined | VariantDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$VariantPayload, S>

export type VariantCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<VariantFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: VariantCountAggregateInputType | true
  }

export interface VariantDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Variant'], meta: { name: 'Variant' } }
  /**
   * Find zero or one Variant that matches the filter.
   * @param {VariantFindUniqueArgs} args - Arguments to find a Variant
   * @example
   * // Get one Variant
   * const variant = await prisma.variant.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends VariantFindUniqueArgs>(args: Prisma.SelectSubset<T, VariantFindUniqueArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Variant that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {VariantFindUniqueOrThrowArgs} args - Arguments to find a Variant
   * @example
   * // Get one Variant
   * const variant = await prisma.variant.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends VariantFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, VariantFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Variant that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VariantFindFirstArgs} args - Arguments to find a Variant
   * @example
   * // Get one Variant
   * const variant = await prisma.variant.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends VariantFindFirstArgs>(args?: Prisma.SelectSubset<T, VariantFindFirstArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Variant that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VariantFindFirstOrThrowArgs} args - Arguments to find a Variant
   * @example
   * // Get one Variant
   * const variant = await prisma.variant.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends VariantFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, VariantFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Variants that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VariantFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Variants
   * const variants = await prisma.variant.findMany()
   * 
   * // Get first 10 Variants
   * const variants = await prisma.variant.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const variantWithIdOnly = await prisma.variant.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends VariantFindManyArgs>(args?: Prisma.SelectSubset<T, VariantFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Variant.
   * @param {VariantCreateArgs} args - Arguments to create a Variant.
   * @example
   * // Create one Variant
   * const Variant = await prisma.variant.create({
   *   data: {
   *     // ... data to create a Variant
   *   }
   * })
   * 
   */
  create<T extends VariantCreateArgs>(args: Prisma.SelectSubset<T, VariantCreateArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Variants.
   * @param {VariantCreateManyArgs} args - Arguments to create many Variants.
   * @example
   * // Create many Variants
   * const variant = await prisma.variant.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends VariantCreateManyArgs>(args?: Prisma.SelectSubset<T, VariantCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Variants and returns the data saved in the database.
   * @param {VariantCreateManyAndReturnArgs} args - Arguments to create many Variants.
   * @example
   * // Create many Variants
   * const variant = await prisma.variant.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Variants and only return the `id`
   * const variantWithIdOnly = await prisma.variant.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends VariantCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, VariantCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Variant.
   * @param {VariantDeleteArgs} args - Arguments to delete one Variant.
   * @example
   * // Delete one Variant
   * const Variant = await prisma.variant.delete({
   *   where: {
   *     // ... filter to delete one Variant
   *   }
   * })
   * 
   */
  delete<T extends VariantDeleteArgs>(args: Prisma.SelectSubset<T, VariantDeleteArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Variant.
   * @param {VariantUpdateArgs} args - Arguments to update one Variant.
   * @example
   * // Update one Variant
   * const variant = await prisma.variant.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends VariantUpdateArgs>(args: Prisma.SelectSubset<T, VariantUpdateArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Variants.
   * @param {VariantDeleteManyArgs} args - Arguments to filter Variants to delete.
   * @example
   * // Delete a few Variants
   * const { count } = await prisma.variant.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends VariantDeleteManyArgs>(args?: Prisma.SelectSubset<T, VariantDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Variants.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VariantUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Variants
   * const variant = await prisma.variant.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends VariantUpdateManyArgs>(args: Prisma.SelectSubset<T, VariantUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Variants and returns the data updated in the database.
   * @param {VariantUpdateManyAndReturnArgs} args - Arguments to update many Variants.
   * @example
   * // Update many Variants
   * const variant = await prisma.variant.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Variants and only return the `id`
   * const variantWithIdOnly = await prisma.variant.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends VariantUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, VariantUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Variant.
   * @param {VariantUpsertArgs} args - Arguments to update or create a Variant.
   * @example
   * // Update or create a Variant
   * const variant = await prisma.variant.upsert({
   *   create: {
   *     // ... data to create a Variant
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Variant we want to update
   *   }
   * })
   */
  upsert<T extends VariantUpsertArgs>(args: Prisma.SelectSubset<T, VariantUpsertArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Variants.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VariantCountArgs} args - Arguments to filter Variants to count.
   * @example
   * // Count the number of Variants
   * const count = await prisma.variant.count({
   *   where: {
   *     // ... the filter for the Variants we want to count
   *   }
   * })
  **/
  count<T extends VariantCountArgs>(
    args?: Prisma.Subset<T, VariantCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], VariantCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Variant.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VariantAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends VariantAggregateArgs>(args: Prisma.Subset<T, VariantAggregateArgs>): Prisma.PrismaPromise<GetVariantAggregateType<T>>

  /**
   * Group by Variant.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {VariantGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends VariantGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: VariantGroupByArgs['orderBy'] }
      : { orderBy?: VariantGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, VariantGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetVariantGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Variant model
 */
readonly fields: VariantFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Variant.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__VariantClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  product<T extends Prisma.ProductDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ProductDefaultArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  orders<T extends Prisma.Variant$ordersArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Variant$ordersArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  stock<T extends Prisma.Variant$stockArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Variant$stockArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Variant model
 */
export interface VariantFieldRefs {
  readonly id: Prisma.FieldRef<"Variant", 'String'>
  readonly name: Prisma.FieldRef<"Variant", 'String'>
  readonly description: Prisma.FieldRef<"Variant", 'String'>
  readonly price: Prisma.FieldRef<"Variant", 'Decimal'>
  readonly productId: Prisma.FieldRef<"Variant", 'String'>
  readonly createdAt: Prisma.FieldRef<"Variant", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Variant", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Variant findUnique
 */
export type VariantFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * Filter, which Variant to fetch.
   */
  where: Prisma.VariantWhereUniqueInput
}

/**
 * Variant findUniqueOrThrow
 */
export type VariantFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * Filter, which Variant to fetch.
   */
  where: Prisma.VariantWhereUniqueInput
}

/**
 * Variant findFirst
 */
export type VariantFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * Filter, which Variant to fetch.
   */
  where?: Prisma.VariantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Variants to fetch.
   */
  orderBy?: Prisma.VariantOrderByWithRelationInput | Prisma.VariantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Variants.
   */
  cursor?: Prisma.VariantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Variants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Variants.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Variants.
   */
  distinct?: Prisma.VariantScalarFieldEnum | Prisma.VariantScalarFieldEnum[]
}

/**
 * Variant findFirstOrThrow
 */
export type VariantFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * Filter, which Variant to fetch.
   */
  where?: Prisma.VariantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Variants to fetch.
   */
  orderBy?: Prisma.VariantOrderByWithRelationInput | Prisma.VariantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Variants.
   */
  cursor?: Prisma.VariantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Variants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Variants.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Variants.
   */
  distinct?: Prisma.VariantScalarFieldEnum | Prisma.VariantScalarFieldEnum[]
}

/**
 * Variant findMany
 */
export type VariantFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * Filter, which Variants to fetch.
   */
  where?: Prisma.VariantWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Variants to fetch.
   */
  orderBy?: Prisma.VariantOrderByWithRelationInput | Prisma.VariantOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Variants.
   */
  cursor?: Prisma.VariantWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Variants from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Variants.
   */
  skip?: number
  distinct?: Prisma.VariantScalarFieldEnum | Prisma.VariantScalarFieldEnum[]
}

/**
 * Variant create
 */
export type VariantCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * The data needed to create a Variant.
   */
  data: Prisma.XOR<Prisma.VariantCreateInput, Prisma.VariantUncheckedCreateInput>
}

/**
 * Variant createMany
 */
export type VariantCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Variants.
   */
  data: Prisma.VariantCreateManyInput | Prisma.VariantCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Variant createManyAndReturn
 */
export type VariantCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * The data used to create many Variants.
   */
  data: Prisma.VariantCreateManyInput | Prisma.VariantCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Variant update
 */
export type VariantUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * The data needed to update a Variant.
   */
  data: Prisma.XOR<Prisma.VariantUpdateInput, Prisma.VariantUncheckedUpdateInput>
  /**
   * Choose, which Variant to update.
   */
  where: Prisma.VariantWhereUniqueInput
}

/**
 * Variant updateMany
 */
export type VariantUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Variants.
   */
  data: Prisma.XOR<Prisma.VariantUpdateManyMutationInput, Prisma.VariantUncheckedUpdateManyInput>
  /**
   * Filter which Variants to update
   */
  where?: Prisma.VariantWhereInput
  /**
   * Limit how many Variants to update.
   */
  limit?: number
}

/**
 * Variant updateManyAndReturn
 */
export type VariantUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * The data used to update Variants.
   */
  data: Prisma.XOR<Prisma.VariantUpdateManyMutationInput, Prisma.VariantUncheckedUpdateManyInput>
  /**
   * Filter which Variants to update
   */
  where?: Prisma.VariantWhereInput
  /**
   * Limit how many Variants to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Variant upsert
 */
export type VariantUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * The filter to search for the Variant to update in case it exists.
   */
  where: Prisma.VariantWhereUniqueInput
  /**
   * In case the Variant found by the `where` argument doesn't exist, create a new Variant with this data.
   */
  create: Prisma.XOR<Prisma.VariantCreateInput, Prisma.VariantUncheckedCreateInput>
  /**
   * In case the Variant was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.VariantUpdateInput, Prisma.VariantUncheckedUpdateInput>
}

/**
 * Variant delete
 */
export type VariantDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  /**
   * Filter which Variant to delete.
   */
  where: Prisma.VariantWhereUniqueInput
}

/**
 * Variant deleteMany
 */
export type VariantDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Variants to delete
   */
  where?: Prisma.VariantWhereInput
  /**
   * Limit how many Variants to delete.
   */
  limit?: number
}

/**
 * Variant.orders
 */
export type Variant$ordersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  where?: Prisma.OrderWhereInput
  orderBy?: Prisma.OrderOrderByWithRelationInput | Prisma.OrderOrderByWithRelationInput[]
  cursor?: Prisma.OrderWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.OrderScalarFieldEnum | Prisma.OrderScalarFieldEnum[]
}

/**
 * Variant.stock
 */
export type Variant$stockArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  where?: Prisma.StockWhereInput
  orderBy?: Prisma.StockOrderByWithRelationInput | Prisma.StockOrderByWithRelationInput[]
  cursor?: Prisma.StockWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.StockScalarFieldEnum | Prisma.StockScalarFieldEnum[]
}

/**
 * Variant without action
 */
export type VariantDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
}
