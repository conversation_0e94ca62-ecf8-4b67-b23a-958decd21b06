import { z } from "zod";
import { router, publicProcedure, protectedProcedure } from "../lib/trpc";
import prisma from "../../prisma";
import { TRPCError } from "@trpc/server";

// Input validation schemas
const createStockSchema = z.object({
  name: z.string().min(1, "Account name is required"),
  email: z.string().email("Valid email is required"),
  password: z.string().min(1, "Password is required"),
  variantId: z.string().min(1, "Variant ID is required"),
});

const createBulkStockSchema = z.object({
  variantId: z.string().min(1, "Variant ID is required"),
  stocks: z
    .array(
      z.object({
        name: z.string().min(1, "Account name is required"),
        email: z.string().email("Valid email is required"),
        password: z.string().min(1, "Password is required"),
      })
    )
    .min(1, "At least one stock item is required"),
});

const updateStockSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Account name is required").optional(),
  email: z.string().email("Valid email is required").optional(),
  password: z.string().min(1, "Password is required").optional(),
  status: z.enum(["AVAILABLE", "ASSIGNED", "SOLD"]).optional(),
});

const stockIdSchema = z.object({
  id: z.string(),
});

const variantIdSchema = z.object({
  variantId: z.string(),
});

const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  variantId: z.string().optional(),
  status: z.enum(["AVAILABLE", "ASSIGNED", "SOLD"]).optional(),
  search: z.string().optional(),
});

const assignStockSchema = z.object({
  stockIds: z.array(z.string()).min(1, "At least one stock ID is required"),
  orderId: z.string().min(1, "Order ID is required"),
});

export const stockRouter = router({
  // Get all stock items with pagination and filters
  getAll: protectedProcedure
    .input(paginationSchema)
    .query(async ({ input }) => {
      const { page, limit, variantId, status, search } = input;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (variantId) where.variantId = variantId;
      if (status) where.status = status;
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" as const } },
          { email: { contains: search, mode: "insensitive" as const } },
        ];
      }

      const [stocks, total] = await Promise.all([
        prisma.stock.findMany({
          where,
          skip,
          take: limit,
          include: {
            variant: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                  },
                },
              },
            },
            order: {
              select: {
                id: true,
                invoiceNumber: true,
                buyerEmail: true,
                status: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        prisma.stock.count({ where }),
      ]);

      return {
        stocks,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // Get stock items by variant ID
  getByVariantId: protectedProcedure
    .input(variantIdSchema)
    .query(async ({ input }) => {
      const stocks = await prisma.stock.findMany({
        where: { variantId: input.variantId },
        include: {
          variant: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
          },
          order: {
            select: {
              id: true,
              invoiceNumber: true,
              buyerEmail: true,
              status: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      return stocks;
    }),

  // Get stock item by ID
  getById: protectedProcedure.input(stockIdSchema).query(async ({ input }) => {
    const stock = await prisma.stock.findUnique({
      where: { id: input.id },
      include: {
        variant: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
        order: {
          select: {
            id: true,
            invoiceNumber: true,
            buyerEmail: true,
            status: true,
            createdAt: true,
          },
        },
      },
    });

    if (!stock) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Stock item not found",
      });
    }

    return stock;
  }),

  // Create single stock item (protected)
  create: protectedProcedure
    .input(createStockSchema)
    .mutation(async ({ input }) => {
      try {
        // Check if variant exists
        const variant = await prisma.variant.findUnique({
          where: { id: input.variantId },
        });

        if (!variant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Variant not found",
          });
        }

        // Generate BOKUL ID
        const lastStock = await prisma.stock.findFirst({
          orderBy: { createdAt: "desc" },
          select: { id: true },
        });

        let nextNumber = 1;
        if (lastStock?.id) {
          const match = lastStock.id.match(/BOKUL-(\d+)/);
          if (match) {
            nextNumber = parseInt(match[1]) + 1;
          }
        }

        const stockId = `BOKUL-${nextNumber.toString().padStart(3, "0")}`;

        const stock = await prisma.stock.create({
          data: {
            id: stockId,
            name: input.name,
            email: input.email,
            password: input.password,
            variantId: input.variantId,
            status: "AVAILABLE",
          },
          include: {
            variant: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        });

        return stock;
      } catch (error: any) {
        if (error instanceof TRPCError) {
          throw error;
        }
        if (error.code === "P2002") {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Stock with this email already exists",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create stock item",
        });
      }
    }),

  // Create multiple stock items (protected)
  createBulk: protectedProcedure
    .input(createBulkStockSchema)
    .mutation(async ({ input }) => {
      try {
        // Check if variant exists
        const variant = await prisma.variant.findUnique({
          where: { id: input.variantId },
        });

        if (!variant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Variant not found",
          });
        }

        // Get the last stock ID for numbering
        const lastStock = await prisma.stock.findFirst({
          orderBy: { createdAt: "desc" },
          select: { id: true },
        });

        let nextNumber = 1;
        if (lastStock?.id) {
          const match = lastStock.id.match(/BOKUL-(\d+)/);
          if (match) {
            nextNumber = parseInt(match[1]) + 1;
          }
        }

        // Prepare stock data with generated IDs
        const stocksData = input.stocks.map((stock, index) => ({
          id: `BOKUL-${(nextNumber + index).toString().padStart(3, "0")}`,
          name: stock.name,
          email: stock.email,
          password: stock.password,
          variantId: input.variantId,
          status: "AVAILABLE" as const,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

        // Create all stocks in a transaction
        const createdStocks = await prisma.$transaction(
          stocksData.map((stockData) =>
            prisma.stock.create({
              data: stockData,
              include: {
                variant: {
                  include: {
                    product: {
                      select: {
                        id: true,
                        name: true,
                        slug: true,
                      },
                    },
                  },
                },
              },
            })
          )
        );

        return {
          success: true,
          created: createdStocks.length,
          stocks: createdStocks,
        };
      } catch (error: any) {
        if (error instanceof TRPCError) {
          throw error;
        }
        if (error.code === "P2002") {
          throw new TRPCError({
            code: "CONFLICT",
            message: "One or more emails already exist",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create stock items",
        });
      }
    }),

  // Update stock item (protected)
  update: protectedProcedure
    .input(updateStockSchema)
    .mutation(async ({ input }) => {
      const { id, ...updateData } = input;

      try {
        const stock = await prisma.stock.update({
          where: { id },
          data: {
            ...updateData,
            updatedAt: new Date(),
          },
          include: {
            variant: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                  },
                },
              },
            },
            order: {
              select: {
                id: true,
                invoiceNumber: true,
                buyerEmail: true,
                status: true,
              },
            },
          },
        });

        return stock;
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Stock item not found",
          });
        }
        if (error.code === "P2002") {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Stock with this email already exists",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update stock item",
        });
      }
    }),

  // Delete stock item (protected)
  delete: protectedProcedure
    .input(stockIdSchema)
    .mutation(async ({ input }) => {
      try {
        // Check if stock is assigned to an order
        const stock = await prisma.stock.findUnique({
          where: { id: input.id },
          include: { order: true },
        });

        if (!stock) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Stock item not found",
          });
        }

        if (stock.status === "SOLD" || stock.order) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot delete stock item that has been sold",
          });
        }

        await prisma.stock.delete({
          where: { id: input.id },
        });

        return { success: true };
      } catch (error: any) {
        if (error instanceof TRPCError) {
          throw error;
        }
        if (error.code === "P2025") {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Stock item not found",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete stock item",
        });
      }
    }),

  // Assign stock to order (protected)
  assignToOrder: protectedProcedure
    .input(assignStockSchema)
    .mutation(async ({ input }) => {
      try {
        // Update stock items to ASSIGNED status and link to order
        const updatedStocks = await prisma.$transaction(
          input.stockIds.map((stockId) =>
            prisma.stock.update({
              where: { id: stockId },
              data: {
                status: "ASSIGNED",
                soldTo: input.orderId,
                updatedAt: new Date(),
              },
            })
          )
        );

        return {
          success: true,
          assignedCount: updatedStocks.length,
        };
      } catch (error: any) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to assign stock to order",
        });
      }
    }),

  // Get available stock count by variant
  getAvailableCount: publicProcedure
    .input(variantIdSchema)
    .query(async ({ input }) => {
      const count = await prisma.stock.count({
        where: {
          variantId: input.variantId,
          status: "AVAILABLE",
        },
      });

      return { count };
    }),

  // Get stock statistics (protected)
  getStats: protectedProcedure.query(async () => {
    const [totalStock, stockByStatus] = await Promise.all([
      prisma.stock.count(),
      prisma.stock.groupBy({
        by: ["status"],
        _count: {
          id: true,
        },
      }),
    ]);

    return {
      totalStock,
      stockByStatus: stockByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.id;
        return acc;
      }, {} as Record<string, number>),
    };
  }),
});
