import Loader from "@/components/loader";
import PageHeader from "@/components/pages/page-header";
import { authClient } from "@/lib/auth-client";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect } from "react";
import StocksTable from "./components/table/stocks-table";

export const Route = createFileRoute("/dashboard/stocks/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { data: session, isPending } = authClient.useSession();

  const navigate = Route.useNavigate();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/login",
      });
    }
  }, [session, isPending]);

  if (isPending) {
    return <Loader />;
  }

  return (
    <div className="space-y-6 w-full max-w-full overflow-hidden">
      <PageHeader
        title="Stock Management"
        description="Manage your stock items including account credentials and their availability status"
      />
      <StocksTable />
    </div>
  );
}
