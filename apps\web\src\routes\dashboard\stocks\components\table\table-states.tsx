import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";

export function LoadingState() {
  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      {/* Loading skeleton for table */}
      <div className="bg-background rounded-md border w-full max-w-full">
        <ScrollArea className="w-full">
          <div className="min-w-[800px]">
            {/* Table header skeleton */}
            <div className="border-b">
              <div className="flex items-center h-12 px-3">
                <div className="flex items-center gap-4 w-full">
                  <Skeleton className="h-4 w-4" /> {/* Select checkbox */}
                  <Skeleton className="h-4 w-[180px]" /> {/* Stock ID */}
                  <Skeleton className="h-4 w-[150px]" /> {/* Account Name */}
                  <Skeleton className="h-4 w-[200px]" /> {/* Email */}
                  <Skeleton className="h-4 w-[250px]" /> {/* Product */}
                  <Skeleton className="h-4 w-[120px]" /> {/* Status */}
                  <Skeleton className="h-4 w-[100px]" /> {/* Created */}
                  <Skeleton className="h-4 w-[60px]" /> {/* Actions */}
                </div>
              </div>
            </div>

            {/* Table rows skeleton */}
            <div className="divide-y">
              {Array.from({ length: 5 }).map((_, i) => (
                <TableRowSkeleton key={i} />
              ))}
            </div>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* Loading skeleton for pagination */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <Skeleton className="h-5 w-32" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-20" />
          <div className="flex items-center gap-1">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      </div>
    </div>
  );
}

function TableRowSkeleton() {
  return (
    <div className="flex items-center h-16 px-3">
      <div className="flex items-center gap-4 w-full">
        {/* Select checkbox */}
        <Skeleton className="h-4 w-4" />

        {/* Stock ID column */}
        <div className="flex items-center gap-3 w-[180px]">
          <Skeleton className="h-8 w-8 rounded-md" />
          <Skeleton className="h-4 w-24 font-mono" />
        </div>

        {/* Account Name */}
        <Skeleton className="h-4 w-[150px]" />

        {/* Email */}
        <Skeleton className="h-4 w-[200px]" />

        {/* Product */}
        <div className="flex items-center gap-3 w-[250px]">
          <Skeleton className="h-8 w-8 rounded-md" />
          <div className="space-y-1">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>

        {/* Status */}
        <Skeleton className="h-6 w-[120px] rounded-full" />

        {/* Created */}
        <Skeleton className="h-4 w-[100px]" />

        {/* Actions */}
        <Skeleton className="h-8 w-8 rounded-md" />
      </div>
    </div>
  );
}

interface ErrorStateProps {
  error: { message: string };
  onRetry?: () => void;
}

export function ErrorState({ error, onRetry }: ErrorStateProps) {
  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      {/* Error state for toolbar */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex flex-wrap items-center gap-3">
          <Skeleton className="h-10 w-full min-w-[200px] sm:min-w-60 opacity-50" />
          <Skeleton className="h-10 w-[140px] opacity-50" />
          <Skeleton className="h-10 w-20 opacity-50" />
        </div>
        <Skeleton className="h-10 w-32 opacity-50" />
      </div>

      {/* Error card */}
      <Card className="w-full border-destructive/50 bg-destructive/5">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            Error Loading Stock Items
          </CardTitle>
          <CardDescription>
            We encountered an error while loading the stock items table.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm font-mono bg-destructive/10 p-3 rounded border border-destructive/20">
            {error.message}
          </div>
          {onRetry && (
            <div className="flex flex-wrap items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="border-destructive/30 hover:bg-destructive/10"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <span className="text-sm text-muted-foreground">
                or refresh the page to retry
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Disabled table skeleton to show layout */}
      <div className="bg-background rounded-md border w-full max-w-full opacity-50">
        <ScrollArea className="w-full">
          <div className="min-w-[800px]">
            {/* Disabled table header */}
            <div className="border-b">
              <div className="flex items-center h-12 px-3">
                <div className="flex items-center gap-4 w-full">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-[180px]" />
                  <Skeleton className="h-4 w-[150px]" />
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[120px]" />
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-4 w-[60px]" />
                </div>
              </div>
            </div>

            {/* Disabled table rows */}
            <div className="divide-y">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center h-16 px-3">
                  <div className="flex items-center gap-4 w-full">
                    <Skeleton className="h-4 w-4" />
                    <div className="flex items-center gap-3 w-[180px]">
                      <Skeleton className="h-8 w-8 rounded-md" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="h-4 w-[150px]" />
                    <Skeleton className="h-4 w-[200px]" />
                    <div className="flex items-center gap-3 w-[250px]">
                      <Skeleton className="h-8 w-8 rounded-md" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                    <Skeleton className="h-6 w-[120px] rounded-full" />
                    <Skeleton className="h-4 w-[100px]" />
                    <Skeleton className="h-8 w-8 rounded-md" />
                  </div>
                </div>
              ))}
            </div>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* Disabled pagination skeleton */}
      <div className="flex flex-wrap items-center justify-between gap-4 opacity-50">
        <Skeleton className="h-5 w-32" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-20" />
          <div className="flex items-center gap-1">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      </div>
    </div>
  );
}
