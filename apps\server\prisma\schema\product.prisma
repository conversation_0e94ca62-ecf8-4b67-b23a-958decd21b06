enum StockStatus {
    AVAILABLE
    ASSIGNED
    SOLD
}

model Product {
    id          String    @id @default(uuid())
    name        String
    description String?
    slug        String    @unique
    variants    Variant[]
    createdAt   DateTime
    updatedAt   DateTime

    @@index([name])
    @@map("product")
}

model Variant {
    id          String  @id @default(uuid())
    name        String
    description String?
    price       Decimal

    productId String
    product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
    orders    Order[]
    stock     Stock[]

    createdAt DateTime
    updatedAt DateTime

    @@index([productId])
    @@map("variant")
}

model Stock {
    id       String      @id @default(uuid())
    status   StockStatus @default(AVAILABLE)
    name     String
    email    String
    password String

    soldTo String?
    order  Order?  @relation(fields: [soldTo], references: [id], onDelete: SetNull)

    variantId String
    variant   Variant @relation(fields: [variantId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now()) @map("created_at")
    updatedAt DateTime @updatedAt @map("updated_at")

    @@index([variantId, status])
    @@index([soldTo])
    @@index([email])
    @@map("stocks")
}
