// Common types for product management

export interface PaginationParams {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationParams;
}

export interface ProductWithVariants {
  id: string;
  name: string;
  description?: string;
  slug: string;
  variants: VariantWithStock[];
  createdAt: Date;
  updatedAt: Date;
}

export interface VariantWithStock {
  id: string;
  name: string;
  description?: string;
  price: number;
  productId: string;
  stock: StockSummary[];
  createdAt: Date;
  updatedAt: Date;
}

export interface StockSummary {
  id: string;
  status: "AVAILABLE" | "ASSIGNED" | "SOLD";
}

export interface StockItem {
  id: string;
  name: string;
  email: string;
  password: string;
  status: "AVAILABLE" | "ASSIGNED" | "SOLD";
  variantId: string;
  soldTo?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductStats {
  totalProducts: number;
  totalVariants: number;
  totalStock: number;
  stockByStatus: Record<string, number>;
}

export interface VariantStats {
  stockCount: number;
  orderCount: number;
  totalSold: number;
  stockByStatus: Record<string, number>;
}

export interface StockStats {
  totalStock: number;
  stockByStatus: Record<string, number>;
}
