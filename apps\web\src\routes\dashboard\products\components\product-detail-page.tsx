import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link, useNavigate } from "@tanstack/react-router";
import { ArrowLeft, ArrowLeftIcon, EditIcon, PlusIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { trpc } from "@/utils/trpc";
import Loader from "@/components/loader";
import PageHeader from "@/components/pages/page-header";
import { EditProductDialog } from "./dialog/edit-product-dialog";
import { CreateVariantDialog } from "./dialog/create-variant-dialog";
import { VariantsTable } from "./variants-table";

interface ProductDetailPageProps {
  slug: string;
}

export function ProductDetailPage({ slug }: ProductDetailPageProps) {
  const navigate = useNavigate();
  const [editProductOpen, setEditProductOpen] = useState(false);
  const [createVariantOpen, setCreateVariantOpen] = useState(false);

  const {
    data: product,
    isLoading,
    error,
    refetch,
  } = useQuery(trpc.product.getBySlug.queryOptions({ slug }));

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate({ to: "/dashboard/products" })}
          >
            <ArrowLeftIcon className="h-4 w-4" />
            Back to Products
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-destructive">
                Product Not Found
              </h3>
              <p className="text-muted-foreground mt-2">
                The product with slug "{slug}" could not be found.
              </p>
              <Button
                className="mt-4"
                onClick={() => navigate({ to: "/dashboard/products" })}
              >
                Back to Products
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!product) {
    return null;
  }

  const totalStock = product.variants.reduce(
    (acc, variant) => acc + variant.stock.length,
    0
  );

  const availableStock = product.variants.reduce(
    (acc, variant) =>
      acc + variant.stock.filter((s) => s.status === "AVAILABLE").length,
    0
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex flex-col">
            <Link
              to="/dashboard/products"
              className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline mb-4"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Products
            </Link>
            <PageHeader
              title={product.name}
              description={`Manage ${product.name} and its variants`}
            />
          </div>
        </div>
        <Button onClick={() => setEditProductOpen(true)}>
          <EditIcon className="h-4 w-4" />
          Edit Product
        </Button>
      </div>

      {/* Product Information */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Product Information</CardTitle>
            <CardDescription>Basic details about this product</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Name
              </label>
              <p className="text-lg font-semibold">{product.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Slug
              </label>
              <p className="text-sm font-mono bg-muted px-2 py-1 rounded">
                /dashboard/products/{product.slug}
              </p>
            </div>
            {product.description && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Description
                </label>
                <p className="text-sm">{product.description}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Created
              </label>
              <p className="text-sm">
                {new Date(product.createdAt).toLocaleDateString()}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Stats Card */}
        <Card>
          <CardHeader>
            <CardTitle>Overview</CardTitle>
            <CardDescription>Product statistics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Variants</span>
              <Badge variant="secondary">{product.variants.length}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Stock</span>
              <Badge variant="outline">{totalStock}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Available</span>
              <Badge variant={availableStock > 0 ? "default" : "secondary"}>
                {availableStock}
              </Badge>
            </div>
            {product.variants.length > 0 && (
              <div className="pt-2 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Price Range</span>
                  <span className="text-sm">
                    Rp
                    {Math.min(
                      ...product.variants.map((v) => Number(v.price))
                    ).toLocaleString()}{" "}
                    - Rp
                    {Math.max(
                      ...product.variants.map((v) => Number(v.price))
                    ).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Variants Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Variants</CardTitle>
              <CardDescription>
                Manage different options and pricing for this product
              </CardDescription>
            </div>
            <Button onClick={() => setCreateVariantOpen(true)}>
              <PlusIcon className="h-4 w-4" />
              Add Variant
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <VariantsTable
            productId={product.id}
            variants={product.variants}
            onUpdate={refetch}
          />
        </CardContent>
      </Card>

      {/* Dialogs */}
      <EditProductDialog
        product={product}
        open={editProductOpen}
        onOpenChange={setEditProductOpen}
        onSuccess={refetch}
      />
      <CreateVariantDialog
        productId={product.id}
        open={createVariantOpen}
        onOpenChange={setCreateVariantOpen}
        onSuccess={refetch}
      />
    </div>
  );
}
