import { protectedProcedure, publicProcedure, router } from "../lib/trpc";
import { productRouter } from "./product";
import { variantRouter } from "./variant";
import { stockRouter } from "./stock";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  privateData: protectedProcedure.query(({ ctx }) => {
    return {
      message: "This is private",
      user: ctx.session.user,
    };
  }),

  // Product management routers
  product: productRouter,
  variant: variantRouter,
  stock: stockRouter,
});

export type AppRouter = typeof appRouter;
