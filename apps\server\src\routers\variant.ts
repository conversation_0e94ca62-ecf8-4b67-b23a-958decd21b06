import { z } from "zod";
import { router, publicProcedure, protectedProcedure } from "../lib/trpc";
import prisma from "../../prisma";
import { TRPCError } from "@trpc/server";

// Input validation schemas
const createVariantSchema = z.object({
  name: z.string().min(1, "Variant name is required"),
  description: z.string().optional(),
  price: z.number().positive("Price must be positive"),
  productId: z.string().min(1, "Product ID is required"),
});

const updateVariantSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Variant name is required").optional(),
  description: z.string().optional(),
  price: z.number().positive("Price must be positive").optional(),
});

const variantIdSchema = z.object({
  id: z.string(),
});

const productIdSchema = z.object({
  productId: z.string(),
});

const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  productId: z.string().optional(),
});

export const variantRouter = router({
  // Get all variants with pagination
  getAll: publicProcedure
    .input(paginationSchema)
    .query(async ({ input }) => {
      const { page, limit, productId } = input;
      const skip = (page - 1) * limit;

      const where = productId ? { productId } : {};

      const [variants, total] = await Promise.all([
        prisma.variant.findMany({
          where,
          skip,
          take: limit,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            stock: {
              select: {
                id: true,
                status: true,
              },
            },
            orders: {
              select: {
                id: true,
                status: true,
                quantity: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }),
        prisma.variant.count({ where }),
      ]);

      return {
        variants,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // Get variants by product ID
  getByProductId: publicProcedure
    .input(productIdSchema)
    .query(async ({ input }) => {
      const variants = await prisma.variant.findMany({
        where: { productId: input.productId },
        include: {
          stock: {
            select: {
              id: true,
              status: true,
            },
          },
          orders: {
            select: {
              id: true,
              status: true,
              quantity: true,
            },
          },
        },
        orderBy: { createdAt: "asc" },
      });

      return variants;
    }),

  // Get variant by ID
  getById: publicProcedure
    .input(variantIdSchema)
    .query(async ({ input }) => {
      const variant = await prisma.variant.findUnique({
        where: { id: input.id },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          stock: {
            select: {
              id: true,
              status: true,
              name: true,
              email: true,
            },
          },
          orders: {
            select: {
              id: true,
              status: true,
              quantity: true,
              buyerEmail: true,
              createdAt: true,
            },
          },
        },
      });

      if (!variant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Variant not found",
        });
      }

      return variant;
    }),

  // Create new variant (protected)
  create: protectedProcedure
    .input(createVariantSchema)
    .mutation(async ({ input }) => {
      try {
        // Check if product exists
        const product = await prisma.product.findUnique({
          where: { id: input.productId },
        });

        if (!product) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Product not found",
          });
        }

        const variant = await prisma.variant.create({
          data: {
            ...input,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            stock: true,
          },
        });

        return variant;
      } catch (error: any) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create variant",
        });
      }
    }),

  // Update variant (protected)
  update: protectedProcedure
    .input(updateVariantSchema)
    .mutation(async ({ input }) => {
      const { id, ...updateData } = input;

      try {
        const variant = await prisma.variant.update({
          where: { id },
          data: {
            ...updateData,
            updatedAt: new Date(),
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            stock: true,
          },
        });

        return variant;
      } catch (error: any) {
        if (error.code === "P2025") {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Variant not found",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update variant",
        });
      }
    }),

  // Delete variant (protected)
  delete: protectedProcedure
    .input(variantIdSchema)
    .mutation(async ({ input }) => {
      try {
        // Check if variant has any stock or orders
        const variant = await prisma.variant.findUnique({
          where: { id: input.id },
          include: {
            stock: true,
            orders: true,
          },
        });

        if (!variant) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Variant not found",
          });
        }

        if (variant.stock.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot delete variant with existing stock items",
          });
        }

        if (variant.orders.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot delete variant with existing orders",
          });
        }

        await prisma.variant.delete({
          where: { id: input.id },
        });

        return { success: true };
      } catch (error: any) {
        if (error instanceof TRPCError) {
          throw error;
        }
        if (error.code === "P2025") {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Variant not found",
          });
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete variant",
        });
      }
    }),

  // Get variant statistics (protected)
  getStats: protectedProcedure
    .input(variantIdSchema)
    .query(async ({ input }) => {
      const [stockCount, orderCount, totalRevenue] = await Promise.all([
        prisma.stock.count({
          where: { variantId: input.id },
        }),
        prisma.order.count({
          where: { variantId: input.id },
        }),
        prisma.order.aggregate({
          where: {
            variantId: input.id,
            status: "COMPLETED",
          },
          _sum: {
            quantity: true,
          },
        }),
      ]);

      const stockByStatus = await prisma.stock.groupBy({
        by: ["status"],
        where: { variantId: input.id },
        _count: {
          id: true,
        },
      });

      return {
        stockCount,
        orderCount,
        totalSold: totalRevenue._sum.quantity || 0,
        stockByStatus: stockByStatus.reduce(
          (acc, item) => {
            acc[item.status] = item._count.id;
            return acc;
          },
          {} as Record<string, number>
        ),
      };
    }),
});
