import { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { useNavigate } from "@tanstack/react-router";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";
import { PlusIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { trpcClient } from "@/utils/trpc";

// Validation schema
const createProductSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug is required"),
});

type CreateProductFormData = z.infer<typeof createProductSchema>;

interface CreateProductDialogProps {
  onSuccess?: (product: any) => void;
}

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
}

export function CreateProductDialog({ onSuccess }: CreateProductDialogProps) {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();

  const createProductMutation = useMutation({
    mutationFn: async (data: CreateProductFormData) => {
      return trpcClient.product.create.mutate({
        name: data.name,
        description: data.description,
        slug: data.slug,
      });
    },
    onSuccess: (data) => {
      toast.success("Product created successfully!");
      setOpen(false);
      form.reset();

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(data);
      }

      // Navigate to the product detail page
      navigate({
        to: `/dashboard/products/${data.slug}`,
      });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create product");
    },
  });

  const form = useForm({
    defaultValues: {
      name: "",
      description: "",
      slug: "",
    } as CreateProductFormData,
    onSubmit: async ({ value }) => {
      createProductMutation.mutate(value);
    },
    validators: {
      onSubmit: createProductSchema,
    },
  });

  // Auto-generate slug when name changes
  const handleNameChange = (name: string) => {
    form.setFieldValue("name", name);
    if (name) {
      const generatedSlug = generateSlug(name);
      form.setFieldValue("slug", generatedSlug);
    } else {
      form.setFieldValue("slug", "");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild className="cursor-pointer">
        <Button>
          <PlusIcon className="h-4 w-4" />
          Create Product
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Product</DialogTitle>
          <DialogDescription>
            Create a new product for your store. You can add variants after
            creation.
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            void form.handleSubmit();
          }}
          className="space-y-4"
        >
          <div>
            <form.Field name="name">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Product Name</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., Canva Pro"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => handleNameChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="slug">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>URL Slug</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., canva-pro"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    This will be used in the URL: /dashboard/products/
                    {field.state.value || "your-slug"}
                  </p>
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="description">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Description (Optional)</Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    placeholder="Describe your product..."
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    rows={3}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <form.Subscribe>
              {(state) => (
                <Button
                  type="submit"
                  disabled={
                    !state.canSubmit ||
                    state.isSubmitting ||
                    createProductMutation.isPending
                  }
                >
                  {state.isSubmitting || createProductMutation.isPending
                    ? "Creating..."
                    : "Create Product"}
                </Button>
              )}
            </form.Subscribe>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
