
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Stock` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Stock
 * 
 */
export type StockModel = runtime.Types.Result.DefaultSelection<Prisma.$StockPayload>

export type AggregateStock = {
  _count: StockCountAggregateOutputType | null
  _min: StockMinAggregateOutputType | null
  _max: StockMaxAggregateOutputType | null
}

export type StockMinAggregateOutputType = {
  id: string | null
  status: $Enums.StockStatus | null
  name: string | null
  email: string | null
  password: string | null
  soldTo: string | null
  variantId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type StockMaxAggregateOutputType = {
  id: string | null
  status: $Enums.StockStatus | null
  name: string | null
  email: string | null
  password: string | null
  soldTo: string | null
  variantId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type StockCountAggregateOutputType = {
  id: number
  status: number
  name: number
  email: number
  password: number
  soldTo: number
  variantId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type StockMinAggregateInputType = {
  id?: true
  status?: true
  name?: true
  email?: true
  password?: true
  soldTo?: true
  variantId?: true
  createdAt?: true
  updatedAt?: true
}

export type StockMaxAggregateInputType = {
  id?: true
  status?: true
  name?: true
  email?: true
  password?: true
  soldTo?: true
  variantId?: true
  createdAt?: true
  updatedAt?: true
}

export type StockCountAggregateInputType = {
  id?: true
  status?: true
  name?: true
  email?: true
  password?: true
  soldTo?: true
  variantId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type StockAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Stock to aggregate.
   */
  where?: Prisma.StockWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Stocks to fetch.
   */
  orderBy?: Prisma.StockOrderByWithRelationInput | Prisma.StockOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.StockWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Stocks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Stocks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Stocks
  **/
  _count?: true | StockCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: StockMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: StockMaxAggregateInputType
}

export type GetStockAggregateType<T extends StockAggregateArgs> = {
      [P in keyof T & keyof AggregateStock]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateStock[P]>
    : Prisma.GetScalarType<T[P], AggregateStock[P]>
}




export type StockGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.StockWhereInput
  orderBy?: Prisma.StockOrderByWithAggregationInput | Prisma.StockOrderByWithAggregationInput[]
  by: Prisma.StockScalarFieldEnum[] | Prisma.StockScalarFieldEnum
  having?: Prisma.StockScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: StockCountAggregateInputType | true
  _min?: StockMinAggregateInputType
  _max?: StockMaxAggregateInputType
}

export type StockGroupByOutputType = {
  id: string
  status: $Enums.StockStatus
  name: string
  email: string
  password: string
  soldTo: string | null
  variantId: string
  createdAt: Date
  updatedAt: Date
  _count: StockCountAggregateOutputType | null
  _min: StockMinAggregateOutputType | null
  _max: StockMaxAggregateOutputType | null
}

type GetStockGroupByPayload<T extends StockGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<StockGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof StockGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], StockGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], StockGroupByOutputType[P]>
      }
    >
  > 



export type StockWhereInput = {
  AND?: Prisma.StockWhereInput | Prisma.StockWhereInput[]
  OR?: Prisma.StockWhereInput[]
  NOT?: Prisma.StockWhereInput | Prisma.StockWhereInput[]
  id?: Prisma.StringFilter<"Stock"> | string
  status?: Prisma.EnumStockStatusFilter<"Stock"> | $Enums.StockStatus
  name?: Prisma.StringFilter<"Stock"> | string
  email?: Prisma.StringFilter<"Stock"> | string
  password?: Prisma.StringFilter<"Stock"> | string
  soldTo?: Prisma.StringNullableFilter<"Stock"> | string | null
  variantId?: Prisma.StringFilter<"Stock"> | string
  createdAt?: Prisma.DateTimeFilter<"Stock"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Stock"> | Date | string
  order?: Prisma.XOR<Prisma.OrderNullableScalarRelationFilter, Prisma.OrderWhereInput> | null
  variant?: Prisma.XOR<Prisma.VariantScalarRelationFilter, Prisma.VariantWhereInput>
}

export type StockOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  status?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  password?: Prisma.SortOrder
  soldTo?: Prisma.SortOrderInput | Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  order?: Prisma.OrderOrderByWithRelationInput
  variant?: Prisma.VariantOrderByWithRelationInput
}

export type StockWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.StockWhereInput | Prisma.StockWhereInput[]
  OR?: Prisma.StockWhereInput[]
  NOT?: Prisma.StockWhereInput | Prisma.StockWhereInput[]
  status?: Prisma.EnumStockStatusFilter<"Stock"> | $Enums.StockStatus
  name?: Prisma.StringFilter<"Stock"> | string
  email?: Prisma.StringFilter<"Stock"> | string
  password?: Prisma.StringFilter<"Stock"> | string
  soldTo?: Prisma.StringNullableFilter<"Stock"> | string | null
  variantId?: Prisma.StringFilter<"Stock"> | string
  createdAt?: Prisma.DateTimeFilter<"Stock"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Stock"> | Date | string
  order?: Prisma.XOR<Prisma.OrderNullableScalarRelationFilter, Prisma.OrderWhereInput> | null
  variant?: Prisma.XOR<Prisma.VariantScalarRelationFilter, Prisma.VariantWhereInput>
}, "id">

export type StockOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  status?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  password?: Prisma.SortOrder
  soldTo?: Prisma.SortOrderInput | Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.StockCountOrderByAggregateInput
  _max?: Prisma.StockMaxOrderByAggregateInput
  _min?: Prisma.StockMinOrderByAggregateInput
}

export type StockScalarWhereWithAggregatesInput = {
  AND?: Prisma.StockScalarWhereWithAggregatesInput | Prisma.StockScalarWhereWithAggregatesInput[]
  OR?: Prisma.StockScalarWhereWithAggregatesInput[]
  NOT?: Prisma.StockScalarWhereWithAggregatesInput | Prisma.StockScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Stock"> | string
  status?: Prisma.EnumStockStatusWithAggregatesFilter<"Stock"> | $Enums.StockStatus
  name?: Prisma.StringWithAggregatesFilter<"Stock"> | string
  email?: Prisma.StringWithAggregatesFilter<"Stock"> | string
  password?: Prisma.StringWithAggregatesFilter<"Stock"> | string
  soldTo?: Prisma.StringNullableWithAggregatesFilter<"Stock"> | string | null
  variantId?: Prisma.StringWithAggregatesFilter<"Stock"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Stock"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Stock"> | Date | string
}

export type StockCreateInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  createdAt?: Date | string
  updatedAt?: Date | string
  order?: Prisma.OrderCreateNestedOneWithoutStocksInput
  variant: Prisma.VariantCreateNestedOneWithoutStockInput
}

export type StockUncheckedCreateInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  soldTo?: string | null
  variantId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type StockUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  order?: Prisma.OrderUpdateOneWithoutStocksNestedInput
  variant?: Prisma.VariantUpdateOneRequiredWithoutStockNestedInput
}

export type StockUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  soldTo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  variantId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StockCreateManyInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  soldTo?: string | null
  variantId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type StockUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StockUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  soldTo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  variantId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StockListRelationFilter = {
  every?: Prisma.StockWhereInput
  some?: Prisma.StockWhereInput
  none?: Prisma.StockWhereInput
}

export type StockOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type StockCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  status?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  password?: Prisma.SortOrder
  soldTo?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type StockMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  status?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  password?: Prisma.SortOrder
  soldTo?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type StockMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  status?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  password?: Prisma.SortOrder
  soldTo?: Prisma.SortOrder
  variantId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type StockCreateNestedManyWithoutOrderInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutOrderInput, Prisma.StockUncheckedCreateWithoutOrderInput> | Prisma.StockCreateWithoutOrderInput[] | Prisma.StockUncheckedCreateWithoutOrderInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutOrderInput | Prisma.StockCreateOrConnectWithoutOrderInput[]
  createMany?: Prisma.StockCreateManyOrderInputEnvelope
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
}

export type StockUncheckedCreateNestedManyWithoutOrderInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutOrderInput, Prisma.StockUncheckedCreateWithoutOrderInput> | Prisma.StockCreateWithoutOrderInput[] | Prisma.StockUncheckedCreateWithoutOrderInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutOrderInput | Prisma.StockCreateOrConnectWithoutOrderInput[]
  createMany?: Prisma.StockCreateManyOrderInputEnvelope
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
}

export type StockUpdateManyWithoutOrderNestedInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutOrderInput, Prisma.StockUncheckedCreateWithoutOrderInput> | Prisma.StockCreateWithoutOrderInput[] | Prisma.StockUncheckedCreateWithoutOrderInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutOrderInput | Prisma.StockCreateOrConnectWithoutOrderInput[]
  upsert?: Prisma.StockUpsertWithWhereUniqueWithoutOrderInput | Prisma.StockUpsertWithWhereUniqueWithoutOrderInput[]
  createMany?: Prisma.StockCreateManyOrderInputEnvelope
  set?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  disconnect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  delete?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  update?: Prisma.StockUpdateWithWhereUniqueWithoutOrderInput | Prisma.StockUpdateWithWhereUniqueWithoutOrderInput[]
  updateMany?: Prisma.StockUpdateManyWithWhereWithoutOrderInput | Prisma.StockUpdateManyWithWhereWithoutOrderInput[]
  deleteMany?: Prisma.StockScalarWhereInput | Prisma.StockScalarWhereInput[]
}

export type StockUncheckedUpdateManyWithoutOrderNestedInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutOrderInput, Prisma.StockUncheckedCreateWithoutOrderInput> | Prisma.StockCreateWithoutOrderInput[] | Prisma.StockUncheckedCreateWithoutOrderInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutOrderInput | Prisma.StockCreateOrConnectWithoutOrderInput[]
  upsert?: Prisma.StockUpsertWithWhereUniqueWithoutOrderInput | Prisma.StockUpsertWithWhereUniqueWithoutOrderInput[]
  createMany?: Prisma.StockCreateManyOrderInputEnvelope
  set?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  disconnect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  delete?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  update?: Prisma.StockUpdateWithWhereUniqueWithoutOrderInput | Prisma.StockUpdateWithWhereUniqueWithoutOrderInput[]
  updateMany?: Prisma.StockUpdateManyWithWhereWithoutOrderInput | Prisma.StockUpdateManyWithWhereWithoutOrderInput[]
  deleteMany?: Prisma.StockScalarWhereInput | Prisma.StockScalarWhereInput[]
}

export type StockCreateNestedManyWithoutVariantInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutVariantInput, Prisma.StockUncheckedCreateWithoutVariantInput> | Prisma.StockCreateWithoutVariantInput[] | Prisma.StockUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutVariantInput | Prisma.StockCreateOrConnectWithoutVariantInput[]
  createMany?: Prisma.StockCreateManyVariantInputEnvelope
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
}

export type StockUncheckedCreateNestedManyWithoutVariantInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutVariantInput, Prisma.StockUncheckedCreateWithoutVariantInput> | Prisma.StockCreateWithoutVariantInput[] | Prisma.StockUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutVariantInput | Prisma.StockCreateOrConnectWithoutVariantInput[]
  createMany?: Prisma.StockCreateManyVariantInputEnvelope
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
}

export type StockUpdateManyWithoutVariantNestedInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutVariantInput, Prisma.StockUncheckedCreateWithoutVariantInput> | Prisma.StockCreateWithoutVariantInput[] | Prisma.StockUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutVariantInput | Prisma.StockCreateOrConnectWithoutVariantInput[]
  upsert?: Prisma.StockUpsertWithWhereUniqueWithoutVariantInput | Prisma.StockUpsertWithWhereUniqueWithoutVariantInput[]
  createMany?: Prisma.StockCreateManyVariantInputEnvelope
  set?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  disconnect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  delete?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  update?: Prisma.StockUpdateWithWhereUniqueWithoutVariantInput | Prisma.StockUpdateWithWhereUniqueWithoutVariantInput[]
  updateMany?: Prisma.StockUpdateManyWithWhereWithoutVariantInput | Prisma.StockUpdateManyWithWhereWithoutVariantInput[]
  deleteMany?: Prisma.StockScalarWhereInput | Prisma.StockScalarWhereInput[]
}

export type StockUncheckedUpdateManyWithoutVariantNestedInput = {
  create?: Prisma.XOR<Prisma.StockCreateWithoutVariantInput, Prisma.StockUncheckedCreateWithoutVariantInput> | Prisma.StockCreateWithoutVariantInput[] | Prisma.StockUncheckedCreateWithoutVariantInput[]
  connectOrCreate?: Prisma.StockCreateOrConnectWithoutVariantInput | Prisma.StockCreateOrConnectWithoutVariantInput[]
  upsert?: Prisma.StockUpsertWithWhereUniqueWithoutVariantInput | Prisma.StockUpsertWithWhereUniqueWithoutVariantInput[]
  createMany?: Prisma.StockCreateManyVariantInputEnvelope
  set?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  disconnect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  delete?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  connect?: Prisma.StockWhereUniqueInput | Prisma.StockWhereUniqueInput[]
  update?: Prisma.StockUpdateWithWhereUniqueWithoutVariantInput | Prisma.StockUpdateWithWhereUniqueWithoutVariantInput[]
  updateMany?: Prisma.StockUpdateManyWithWhereWithoutVariantInput | Prisma.StockUpdateManyWithWhereWithoutVariantInput[]
  deleteMany?: Prisma.StockScalarWhereInput | Prisma.StockScalarWhereInput[]
}

export type EnumStockStatusFieldUpdateOperationsInput = {
  set?: $Enums.StockStatus
}

export type StockCreateWithoutOrderInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  createdAt?: Date | string
  updatedAt?: Date | string
  variant: Prisma.VariantCreateNestedOneWithoutStockInput
}

export type StockUncheckedCreateWithoutOrderInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  variantId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type StockCreateOrConnectWithoutOrderInput = {
  where: Prisma.StockWhereUniqueInput
  create: Prisma.XOR<Prisma.StockCreateWithoutOrderInput, Prisma.StockUncheckedCreateWithoutOrderInput>
}

export type StockCreateManyOrderInputEnvelope = {
  data: Prisma.StockCreateManyOrderInput | Prisma.StockCreateManyOrderInput[]
  skipDuplicates?: boolean
}

export type StockUpsertWithWhereUniqueWithoutOrderInput = {
  where: Prisma.StockWhereUniqueInput
  update: Prisma.XOR<Prisma.StockUpdateWithoutOrderInput, Prisma.StockUncheckedUpdateWithoutOrderInput>
  create: Prisma.XOR<Prisma.StockCreateWithoutOrderInput, Prisma.StockUncheckedCreateWithoutOrderInput>
}

export type StockUpdateWithWhereUniqueWithoutOrderInput = {
  where: Prisma.StockWhereUniqueInput
  data: Prisma.XOR<Prisma.StockUpdateWithoutOrderInput, Prisma.StockUncheckedUpdateWithoutOrderInput>
}

export type StockUpdateManyWithWhereWithoutOrderInput = {
  where: Prisma.StockScalarWhereInput
  data: Prisma.XOR<Prisma.StockUpdateManyMutationInput, Prisma.StockUncheckedUpdateManyWithoutOrderInput>
}

export type StockScalarWhereInput = {
  AND?: Prisma.StockScalarWhereInput | Prisma.StockScalarWhereInput[]
  OR?: Prisma.StockScalarWhereInput[]
  NOT?: Prisma.StockScalarWhereInput | Prisma.StockScalarWhereInput[]
  id?: Prisma.StringFilter<"Stock"> | string
  status?: Prisma.EnumStockStatusFilter<"Stock"> | $Enums.StockStatus
  name?: Prisma.StringFilter<"Stock"> | string
  email?: Prisma.StringFilter<"Stock"> | string
  password?: Prisma.StringFilter<"Stock"> | string
  soldTo?: Prisma.StringNullableFilter<"Stock"> | string | null
  variantId?: Prisma.StringFilter<"Stock"> | string
  createdAt?: Prisma.DateTimeFilter<"Stock"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Stock"> | Date | string
}

export type StockCreateWithoutVariantInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  createdAt?: Date | string
  updatedAt?: Date | string
  order?: Prisma.OrderCreateNestedOneWithoutStocksInput
}

export type StockUncheckedCreateWithoutVariantInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  soldTo?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type StockCreateOrConnectWithoutVariantInput = {
  where: Prisma.StockWhereUniqueInput
  create: Prisma.XOR<Prisma.StockCreateWithoutVariantInput, Prisma.StockUncheckedCreateWithoutVariantInput>
}

export type StockCreateManyVariantInputEnvelope = {
  data: Prisma.StockCreateManyVariantInput | Prisma.StockCreateManyVariantInput[]
  skipDuplicates?: boolean
}

export type StockUpsertWithWhereUniqueWithoutVariantInput = {
  where: Prisma.StockWhereUniqueInput
  update: Prisma.XOR<Prisma.StockUpdateWithoutVariantInput, Prisma.StockUncheckedUpdateWithoutVariantInput>
  create: Prisma.XOR<Prisma.StockCreateWithoutVariantInput, Prisma.StockUncheckedCreateWithoutVariantInput>
}

export type StockUpdateWithWhereUniqueWithoutVariantInput = {
  where: Prisma.StockWhereUniqueInput
  data: Prisma.XOR<Prisma.StockUpdateWithoutVariantInput, Prisma.StockUncheckedUpdateWithoutVariantInput>
}

export type StockUpdateManyWithWhereWithoutVariantInput = {
  where: Prisma.StockScalarWhereInput
  data: Prisma.XOR<Prisma.StockUpdateManyMutationInput, Prisma.StockUncheckedUpdateManyWithoutVariantInput>
}

export type StockCreateManyOrderInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  variantId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type StockUpdateWithoutOrderInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  variant?: Prisma.VariantUpdateOneRequiredWithoutStockNestedInput
}

export type StockUncheckedUpdateWithoutOrderInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  variantId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StockUncheckedUpdateManyWithoutOrderInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  variantId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StockCreateManyVariantInput = {
  id?: string
  status?: $Enums.StockStatus
  name: string
  email: string
  password: string
  soldTo?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type StockUpdateWithoutVariantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  order?: Prisma.OrderUpdateOneWithoutStocksNestedInput
}

export type StockUncheckedUpdateWithoutVariantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  soldTo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type StockUncheckedUpdateManyWithoutVariantInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumStockStatusFieldUpdateOperationsInput | $Enums.StockStatus
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  password?: Prisma.StringFieldUpdateOperationsInput | string
  soldTo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type StockSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  status?: boolean
  name?: boolean
  email?: boolean
  password?: boolean
  soldTo?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  order?: boolean | Prisma.Stock$orderArgs<ExtArgs>
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["stock"]>

export type StockSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  status?: boolean
  name?: boolean
  email?: boolean
  password?: boolean
  soldTo?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  order?: boolean | Prisma.Stock$orderArgs<ExtArgs>
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["stock"]>

export type StockSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  status?: boolean
  name?: boolean
  email?: boolean
  password?: boolean
  soldTo?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  order?: boolean | Prisma.Stock$orderArgs<ExtArgs>
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}, ExtArgs["result"]["stock"]>

export type StockSelectScalar = {
  id?: boolean
  status?: boolean
  name?: boolean
  email?: boolean
  password?: boolean
  soldTo?: boolean
  variantId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type StockOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "status" | "name" | "email" | "password" | "soldTo" | "variantId" | "createdAt" | "updatedAt", ExtArgs["result"]["stock"]>
export type StockInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  order?: boolean | Prisma.Stock$orderArgs<ExtArgs>
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}
export type StockIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  order?: boolean | Prisma.Stock$orderArgs<ExtArgs>
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}
export type StockIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  order?: boolean | Prisma.Stock$orderArgs<ExtArgs>
  variant?: boolean | Prisma.VariantDefaultArgs<ExtArgs>
}

export type $StockPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Stock"
  objects: {
    order: Prisma.$OrderPayload<ExtArgs> | null
    variant: Prisma.$VariantPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    status: $Enums.StockStatus
    name: string
    email: string
    password: string
    soldTo: string | null
    variantId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["stock"]>
  composites: {}
}

export type StockGetPayload<S extends boolean | null | undefined | StockDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$StockPayload, S>

export type StockCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<StockFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: StockCountAggregateInputType | true
  }

export interface StockDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Stock'], meta: { name: 'Stock' } }
  /**
   * Find zero or one Stock that matches the filter.
   * @param {StockFindUniqueArgs} args - Arguments to find a Stock
   * @example
   * // Get one Stock
   * const stock = await prisma.stock.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends StockFindUniqueArgs>(args: Prisma.SelectSubset<T, StockFindUniqueArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Stock that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {StockFindUniqueOrThrowArgs} args - Arguments to find a Stock
   * @example
   * // Get one Stock
   * const stock = await prisma.stock.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends StockFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, StockFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Stock that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StockFindFirstArgs} args - Arguments to find a Stock
   * @example
   * // Get one Stock
   * const stock = await prisma.stock.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends StockFindFirstArgs>(args?: Prisma.SelectSubset<T, StockFindFirstArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Stock that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StockFindFirstOrThrowArgs} args - Arguments to find a Stock
   * @example
   * // Get one Stock
   * const stock = await prisma.stock.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends StockFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, StockFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Stocks that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StockFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Stocks
   * const stocks = await prisma.stock.findMany()
   * 
   * // Get first 10 Stocks
   * const stocks = await prisma.stock.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const stockWithIdOnly = await prisma.stock.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends StockFindManyArgs>(args?: Prisma.SelectSubset<T, StockFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Stock.
   * @param {StockCreateArgs} args - Arguments to create a Stock.
   * @example
   * // Create one Stock
   * const Stock = await prisma.stock.create({
   *   data: {
   *     // ... data to create a Stock
   *   }
   * })
   * 
   */
  create<T extends StockCreateArgs>(args: Prisma.SelectSubset<T, StockCreateArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Stocks.
   * @param {StockCreateManyArgs} args - Arguments to create many Stocks.
   * @example
   * // Create many Stocks
   * const stock = await prisma.stock.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends StockCreateManyArgs>(args?: Prisma.SelectSubset<T, StockCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Stocks and returns the data saved in the database.
   * @param {StockCreateManyAndReturnArgs} args - Arguments to create many Stocks.
   * @example
   * // Create many Stocks
   * const stock = await prisma.stock.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Stocks and only return the `id`
   * const stockWithIdOnly = await prisma.stock.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends StockCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, StockCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Stock.
   * @param {StockDeleteArgs} args - Arguments to delete one Stock.
   * @example
   * // Delete one Stock
   * const Stock = await prisma.stock.delete({
   *   where: {
   *     // ... filter to delete one Stock
   *   }
   * })
   * 
   */
  delete<T extends StockDeleteArgs>(args: Prisma.SelectSubset<T, StockDeleteArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Stock.
   * @param {StockUpdateArgs} args - Arguments to update one Stock.
   * @example
   * // Update one Stock
   * const stock = await prisma.stock.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends StockUpdateArgs>(args: Prisma.SelectSubset<T, StockUpdateArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Stocks.
   * @param {StockDeleteManyArgs} args - Arguments to filter Stocks to delete.
   * @example
   * // Delete a few Stocks
   * const { count } = await prisma.stock.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends StockDeleteManyArgs>(args?: Prisma.SelectSubset<T, StockDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Stocks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StockUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Stocks
   * const stock = await prisma.stock.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends StockUpdateManyArgs>(args: Prisma.SelectSubset<T, StockUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Stocks and returns the data updated in the database.
   * @param {StockUpdateManyAndReturnArgs} args - Arguments to update many Stocks.
   * @example
   * // Update many Stocks
   * const stock = await prisma.stock.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Stocks and only return the `id`
   * const stockWithIdOnly = await prisma.stock.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends StockUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, StockUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Stock.
   * @param {StockUpsertArgs} args - Arguments to update or create a Stock.
   * @example
   * // Update or create a Stock
   * const stock = await prisma.stock.upsert({
   *   create: {
   *     // ... data to create a Stock
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Stock we want to update
   *   }
   * })
   */
  upsert<T extends StockUpsertArgs>(args: Prisma.SelectSubset<T, StockUpsertArgs<ExtArgs>>): Prisma.Prisma__StockClient<runtime.Types.Result.GetResult<Prisma.$StockPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Stocks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StockCountArgs} args - Arguments to filter Stocks to count.
   * @example
   * // Count the number of Stocks
   * const count = await prisma.stock.count({
   *   where: {
   *     // ... the filter for the Stocks we want to count
   *   }
   * })
  **/
  count<T extends StockCountArgs>(
    args?: Prisma.Subset<T, StockCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], StockCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Stock.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StockAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends StockAggregateArgs>(args: Prisma.Subset<T, StockAggregateArgs>): Prisma.PrismaPromise<GetStockAggregateType<T>>

  /**
   * Group by Stock.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {StockGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends StockGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: StockGroupByArgs['orderBy'] }
      : { orderBy?: StockGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, StockGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStockGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Stock model
 */
readonly fields: StockFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Stock.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__StockClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  order<T extends Prisma.Stock$orderArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Stock$orderArgs<ExtArgs>>): Prisma.Prisma__OrderClient<runtime.Types.Result.GetResult<Prisma.$OrderPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  variant<T extends Prisma.VariantDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.VariantDefaultArgs<ExtArgs>>): Prisma.Prisma__VariantClient<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Stock model
 */
export interface StockFieldRefs {
  readonly id: Prisma.FieldRef<"Stock", 'String'>
  readonly status: Prisma.FieldRef<"Stock", 'StockStatus'>
  readonly name: Prisma.FieldRef<"Stock", 'String'>
  readonly email: Prisma.FieldRef<"Stock", 'String'>
  readonly password: Prisma.FieldRef<"Stock", 'String'>
  readonly soldTo: Prisma.FieldRef<"Stock", 'String'>
  readonly variantId: Prisma.FieldRef<"Stock", 'String'>
  readonly createdAt: Prisma.FieldRef<"Stock", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Stock", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Stock findUnique
 */
export type StockFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * Filter, which Stock to fetch.
   */
  where: Prisma.StockWhereUniqueInput
}

/**
 * Stock findUniqueOrThrow
 */
export type StockFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * Filter, which Stock to fetch.
   */
  where: Prisma.StockWhereUniqueInput
}

/**
 * Stock findFirst
 */
export type StockFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * Filter, which Stock to fetch.
   */
  where?: Prisma.StockWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Stocks to fetch.
   */
  orderBy?: Prisma.StockOrderByWithRelationInput | Prisma.StockOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Stocks.
   */
  cursor?: Prisma.StockWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Stocks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Stocks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Stocks.
   */
  distinct?: Prisma.StockScalarFieldEnum | Prisma.StockScalarFieldEnum[]
}

/**
 * Stock findFirstOrThrow
 */
export type StockFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * Filter, which Stock to fetch.
   */
  where?: Prisma.StockWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Stocks to fetch.
   */
  orderBy?: Prisma.StockOrderByWithRelationInput | Prisma.StockOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Stocks.
   */
  cursor?: Prisma.StockWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Stocks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Stocks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Stocks.
   */
  distinct?: Prisma.StockScalarFieldEnum | Prisma.StockScalarFieldEnum[]
}

/**
 * Stock findMany
 */
export type StockFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * Filter, which Stocks to fetch.
   */
  where?: Prisma.StockWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Stocks to fetch.
   */
  orderBy?: Prisma.StockOrderByWithRelationInput | Prisma.StockOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Stocks.
   */
  cursor?: Prisma.StockWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Stocks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Stocks.
   */
  skip?: number
  distinct?: Prisma.StockScalarFieldEnum | Prisma.StockScalarFieldEnum[]
}

/**
 * Stock create
 */
export type StockCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * The data needed to create a Stock.
   */
  data: Prisma.XOR<Prisma.StockCreateInput, Prisma.StockUncheckedCreateInput>
}

/**
 * Stock createMany
 */
export type StockCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Stocks.
   */
  data: Prisma.StockCreateManyInput | Prisma.StockCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Stock createManyAndReturn
 */
export type StockCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * The data used to create many Stocks.
   */
  data: Prisma.StockCreateManyInput | Prisma.StockCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Stock update
 */
export type StockUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * The data needed to update a Stock.
   */
  data: Prisma.XOR<Prisma.StockUpdateInput, Prisma.StockUncheckedUpdateInput>
  /**
   * Choose, which Stock to update.
   */
  where: Prisma.StockWhereUniqueInput
}

/**
 * Stock updateMany
 */
export type StockUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Stocks.
   */
  data: Prisma.XOR<Prisma.StockUpdateManyMutationInput, Prisma.StockUncheckedUpdateManyInput>
  /**
   * Filter which Stocks to update
   */
  where?: Prisma.StockWhereInput
  /**
   * Limit how many Stocks to update.
   */
  limit?: number
}

/**
 * Stock updateManyAndReturn
 */
export type StockUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * The data used to update Stocks.
   */
  data: Prisma.XOR<Prisma.StockUpdateManyMutationInput, Prisma.StockUncheckedUpdateManyInput>
  /**
   * Filter which Stocks to update
   */
  where?: Prisma.StockWhereInput
  /**
   * Limit how many Stocks to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Stock upsert
 */
export type StockUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * The filter to search for the Stock to update in case it exists.
   */
  where: Prisma.StockWhereUniqueInput
  /**
   * In case the Stock found by the `where` argument doesn't exist, create a new Stock with this data.
   */
  create: Prisma.XOR<Prisma.StockCreateInput, Prisma.StockUncheckedCreateInput>
  /**
   * In case the Stock was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.StockUpdateInput, Prisma.StockUncheckedUpdateInput>
}

/**
 * Stock delete
 */
export type StockDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
  /**
   * Filter which Stock to delete.
   */
  where: Prisma.StockWhereUniqueInput
}

/**
 * Stock deleteMany
 */
export type StockDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Stocks to delete
   */
  where?: Prisma.StockWhereInput
  /**
   * Limit how many Stocks to delete.
   */
  limit?: number
}

/**
 * Stock.order
 */
export type Stock$orderArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Order
   */
  select?: Prisma.OrderSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Order
   */
  omit?: Prisma.OrderOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.OrderInclude<ExtArgs> | null
  where?: Prisma.OrderWhereInput
}

/**
 * Stock without action
 */
export type StockDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Stock
   */
  select?: Prisma.StockSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Stock
   */
  omit?: Prisma.StockOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.StockInclude<ExtArgs> | null
}
