
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Product` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Product
 * 
 */
export type ProductModel = runtime.Types.Result.DefaultSelection<Prisma.$ProductPayload>

export type AggregateProduct = {
  _count: ProductCountAggregateOutputType | null
  _min: ProductMinAggregateOutputType | null
  _max: ProductMaxAggregateOutputType | null
}

export type ProductMinAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  slug: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ProductMaxAggregateOutputType = {
  id: string | null
  name: string | null
  description: string | null
  slug: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ProductCountAggregateOutputType = {
  id: number
  name: number
  description: number
  slug: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ProductMinAggregateInputType = {
  id?: true
  name?: true
  description?: true
  slug?: true
  createdAt?: true
  updatedAt?: true
}

export type ProductMaxAggregateInputType = {
  id?: true
  name?: true
  description?: true
  slug?: true
  createdAt?: true
  updatedAt?: true
}

export type ProductCountAggregateInputType = {
  id?: true
  name?: true
  description?: true
  slug?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ProductAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Product to aggregate.
   */
  where?: Prisma.ProductWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Products to fetch.
   */
  orderBy?: Prisma.ProductOrderByWithRelationInput | Prisma.ProductOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ProductWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Products from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Products.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Products
  **/
  _count?: true | ProductCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ProductMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ProductMaxAggregateInputType
}

export type GetProductAggregateType<T extends ProductAggregateArgs> = {
      [P in keyof T & keyof AggregateProduct]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateProduct[P]>
    : Prisma.GetScalarType<T[P], AggregateProduct[P]>
}




export type ProductGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ProductWhereInput
  orderBy?: Prisma.ProductOrderByWithAggregationInput | Prisma.ProductOrderByWithAggregationInput[]
  by: Prisma.ProductScalarFieldEnum[] | Prisma.ProductScalarFieldEnum
  having?: Prisma.ProductScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ProductCountAggregateInputType | true
  _min?: ProductMinAggregateInputType
  _max?: ProductMaxAggregateInputType
}

export type ProductGroupByOutputType = {
  id: string
  name: string
  description: string | null
  slug: string
  createdAt: Date
  updatedAt: Date
  _count: ProductCountAggregateOutputType | null
  _min: ProductMinAggregateOutputType | null
  _max: ProductMaxAggregateOutputType | null
}

type GetProductGroupByPayload<T extends ProductGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ProductGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ProductGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ProductGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ProductGroupByOutputType[P]>
      }
    >
  > 



export type ProductWhereInput = {
  AND?: Prisma.ProductWhereInput | Prisma.ProductWhereInput[]
  OR?: Prisma.ProductWhereInput[]
  NOT?: Prisma.ProductWhereInput | Prisma.ProductWhereInput[]
  id?: Prisma.StringFilter<"Product"> | string
  name?: Prisma.StringFilter<"Product"> | string
  description?: Prisma.StringNullableFilter<"Product"> | string | null
  slug?: Prisma.StringFilter<"Product"> | string
  createdAt?: Prisma.DateTimeFilter<"Product"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Product"> | Date | string
  variants?: Prisma.VariantListRelationFilter
}

export type ProductOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  slug?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  variants?: Prisma.VariantOrderByRelationAggregateInput
}

export type ProductWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  slug?: string
  AND?: Prisma.ProductWhereInput | Prisma.ProductWhereInput[]
  OR?: Prisma.ProductWhereInput[]
  NOT?: Prisma.ProductWhereInput | Prisma.ProductWhereInput[]
  name?: Prisma.StringFilter<"Product"> | string
  description?: Prisma.StringNullableFilter<"Product"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Product"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Product"> | Date | string
  variants?: Prisma.VariantListRelationFilter
}, "id" | "slug">

export type ProductOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrderInput | Prisma.SortOrder
  slug?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ProductCountOrderByAggregateInput
  _max?: Prisma.ProductMaxOrderByAggregateInput
  _min?: Prisma.ProductMinOrderByAggregateInput
}

export type ProductScalarWhereWithAggregatesInput = {
  AND?: Prisma.ProductScalarWhereWithAggregatesInput | Prisma.ProductScalarWhereWithAggregatesInput[]
  OR?: Prisma.ProductScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ProductScalarWhereWithAggregatesInput | Prisma.ProductScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Product"> | string
  name?: Prisma.StringWithAggregatesFilter<"Product"> | string
  description?: Prisma.StringNullableWithAggregatesFilter<"Product"> | string | null
  slug?: Prisma.StringWithAggregatesFilter<"Product"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Product"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Product"> | Date | string
}

export type ProductCreateInput = {
  id?: string
  name: string
  description?: string | null
  slug: string
  createdAt: Date | string
  updatedAt: Date | string
  variants?: Prisma.VariantCreateNestedManyWithoutProductInput
}

export type ProductUncheckedCreateInput = {
  id?: string
  name: string
  description?: string | null
  slug: string
  createdAt: Date | string
  updatedAt: Date | string
  variants?: Prisma.VariantUncheckedCreateNestedManyWithoutProductInput
}

export type ProductUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  slug?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  variants?: Prisma.VariantUpdateManyWithoutProductNestedInput
}

export type ProductUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  slug?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  variants?: Prisma.VariantUncheckedUpdateManyWithoutProductNestedInput
}

export type ProductCreateManyInput = {
  id?: string
  name: string
  description?: string | null
  slug: string
  createdAt: Date | string
  updatedAt: Date | string
}

export type ProductUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  slug?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProductUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  slug?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProductCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  slug?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ProductMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  slug?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ProductMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  description?: Prisma.SortOrder
  slug?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ProductScalarRelationFilter = {
  is?: Prisma.ProductWhereInput
  isNot?: Prisma.ProductWhereInput
}

export type ProductCreateNestedOneWithoutVariantsInput = {
  create?: Prisma.XOR<Prisma.ProductCreateWithoutVariantsInput, Prisma.ProductUncheckedCreateWithoutVariantsInput>
  connectOrCreate?: Prisma.ProductCreateOrConnectWithoutVariantsInput
  connect?: Prisma.ProductWhereUniqueInput
}

export type ProductUpdateOneRequiredWithoutVariantsNestedInput = {
  create?: Prisma.XOR<Prisma.ProductCreateWithoutVariantsInput, Prisma.ProductUncheckedCreateWithoutVariantsInput>
  connectOrCreate?: Prisma.ProductCreateOrConnectWithoutVariantsInput
  upsert?: Prisma.ProductUpsertWithoutVariantsInput
  connect?: Prisma.ProductWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ProductUpdateToOneWithWhereWithoutVariantsInput, Prisma.ProductUpdateWithoutVariantsInput>, Prisma.ProductUncheckedUpdateWithoutVariantsInput>
}

export type ProductCreateWithoutVariantsInput = {
  id?: string
  name: string
  description?: string | null
  slug: string
  createdAt: Date | string
  updatedAt: Date | string
}

export type ProductUncheckedCreateWithoutVariantsInput = {
  id?: string
  name: string
  description?: string | null
  slug: string
  createdAt: Date | string
  updatedAt: Date | string
}

export type ProductCreateOrConnectWithoutVariantsInput = {
  where: Prisma.ProductWhereUniqueInput
  create: Prisma.XOR<Prisma.ProductCreateWithoutVariantsInput, Prisma.ProductUncheckedCreateWithoutVariantsInput>
}

export type ProductUpsertWithoutVariantsInput = {
  update: Prisma.XOR<Prisma.ProductUpdateWithoutVariantsInput, Prisma.ProductUncheckedUpdateWithoutVariantsInput>
  create: Prisma.XOR<Prisma.ProductCreateWithoutVariantsInput, Prisma.ProductUncheckedCreateWithoutVariantsInput>
  where?: Prisma.ProductWhereInput
}

export type ProductUpdateToOneWithWhereWithoutVariantsInput = {
  where?: Prisma.ProductWhereInput
  data: Prisma.XOR<Prisma.ProductUpdateWithoutVariantsInput, Prisma.ProductUncheckedUpdateWithoutVariantsInput>
}

export type ProductUpdateWithoutVariantsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  slug?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ProductUncheckedUpdateWithoutVariantsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  description?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  slug?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type ProductCountOutputType
 */

export type ProductCountOutputType = {
  variants: number
}

export type ProductCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  variants?: boolean | ProductCountOutputTypeCountVariantsArgs
}

/**
 * ProductCountOutputType without action
 */
export type ProductCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ProductCountOutputType
   */
  select?: Prisma.ProductCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ProductCountOutputType without action
 */
export type ProductCountOutputTypeCountVariantsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.VariantWhereInput
}


export type ProductSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  slug?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  variants?: boolean | Prisma.Product$variantsArgs<ExtArgs>
  _count?: boolean | Prisma.ProductCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["product"]>

export type ProductSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  slug?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["product"]>

export type ProductSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  description?: boolean
  slug?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["product"]>

export type ProductSelectScalar = {
  id?: boolean
  name?: boolean
  description?: boolean
  slug?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ProductOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "description" | "slug" | "createdAt" | "updatedAt", ExtArgs["result"]["product"]>
export type ProductInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  variants?: boolean | Prisma.Product$variantsArgs<ExtArgs>
  _count?: boolean | Prisma.ProductCountOutputTypeDefaultArgs<ExtArgs>
}
export type ProductIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}
export type ProductIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}

export type $ProductPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Product"
  objects: {
    variants: Prisma.$VariantPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    description: string | null
    slug: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["product"]>
  composites: {}
}

export type ProductGetPayload<S extends boolean | null | undefined | ProductDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ProductPayload, S>

export type ProductCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ProductFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ProductCountAggregateInputType | true
  }

export interface ProductDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Product'], meta: { name: 'Product' } }
  /**
   * Find zero or one Product that matches the filter.
   * @param {ProductFindUniqueArgs} args - Arguments to find a Product
   * @example
   * // Get one Product
   * const product = await prisma.product.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ProductFindUniqueArgs>(args: Prisma.SelectSubset<T, ProductFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Product that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ProductFindUniqueOrThrowArgs} args - Arguments to find a Product
   * @example
   * // Get one Product
   * const product = await prisma.product.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ProductFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ProductFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Product that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProductFindFirstArgs} args - Arguments to find a Product
   * @example
   * // Get one Product
   * const product = await prisma.product.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ProductFindFirstArgs>(args?: Prisma.SelectSubset<T, ProductFindFirstArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Product that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProductFindFirstOrThrowArgs} args - Arguments to find a Product
   * @example
   * // Get one Product
   * const product = await prisma.product.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ProductFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ProductFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Products that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProductFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Products
   * const products = await prisma.product.findMany()
   * 
   * // Get first 10 Products
   * const products = await prisma.product.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const productWithIdOnly = await prisma.product.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ProductFindManyArgs>(args?: Prisma.SelectSubset<T, ProductFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Product.
   * @param {ProductCreateArgs} args - Arguments to create a Product.
   * @example
   * // Create one Product
   * const Product = await prisma.product.create({
   *   data: {
   *     // ... data to create a Product
   *   }
   * })
   * 
   */
  create<T extends ProductCreateArgs>(args: Prisma.SelectSubset<T, ProductCreateArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Products.
   * @param {ProductCreateManyArgs} args - Arguments to create many Products.
   * @example
   * // Create many Products
   * const product = await prisma.product.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ProductCreateManyArgs>(args?: Prisma.SelectSubset<T, ProductCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Products and returns the data saved in the database.
   * @param {ProductCreateManyAndReturnArgs} args - Arguments to create many Products.
   * @example
   * // Create many Products
   * const product = await prisma.product.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Products and only return the `id`
   * const productWithIdOnly = await prisma.product.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ProductCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ProductCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Product.
   * @param {ProductDeleteArgs} args - Arguments to delete one Product.
   * @example
   * // Delete one Product
   * const Product = await prisma.product.delete({
   *   where: {
   *     // ... filter to delete one Product
   *   }
   * })
   * 
   */
  delete<T extends ProductDeleteArgs>(args: Prisma.SelectSubset<T, ProductDeleteArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Product.
   * @param {ProductUpdateArgs} args - Arguments to update one Product.
   * @example
   * // Update one Product
   * const product = await prisma.product.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ProductUpdateArgs>(args: Prisma.SelectSubset<T, ProductUpdateArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Products.
   * @param {ProductDeleteManyArgs} args - Arguments to filter Products to delete.
   * @example
   * // Delete a few Products
   * const { count } = await prisma.product.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ProductDeleteManyArgs>(args?: Prisma.SelectSubset<T, ProductDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Products.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProductUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Products
   * const product = await prisma.product.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ProductUpdateManyArgs>(args: Prisma.SelectSubset<T, ProductUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Products and returns the data updated in the database.
   * @param {ProductUpdateManyAndReturnArgs} args - Arguments to update many Products.
   * @example
   * // Update many Products
   * const product = await prisma.product.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Products and only return the `id`
   * const productWithIdOnly = await prisma.product.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ProductUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ProductUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Product.
   * @param {ProductUpsertArgs} args - Arguments to update or create a Product.
   * @example
   * // Update or create a Product
   * const product = await prisma.product.upsert({
   *   create: {
   *     // ... data to create a Product
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Product we want to update
   *   }
   * })
   */
  upsert<T extends ProductUpsertArgs>(args: Prisma.SelectSubset<T, ProductUpsertArgs<ExtArgs>>): Prisma.Prisma__ProductClient<runtime.Types.Result.GetResult<Prisma.$ProductPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Products.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProductCountArgs} args - Arguments to filter Products to count.
   * @example
   * // Count the number of Products
   * const count = await prisma.product.count({
   *   where: {
   *     // ... the filter for the Products we want to count
   *   }
   * })
  **/
  count<T extends ProductCountArgs>(
    args?: Prisma.Subset<T, ProductCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ProductCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Product.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProductAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ProductAggregateArgs>(args: Prisma.Subset<T, ProductAggregateArgs>): Prisma.PrismaPromise<GetProductAggregateType<T>>

  /**
   * Group by Product.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ProductGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ProductGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ProductGroupByArgs['orderBy'] }
      : { orderBy?: ProductGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ProductGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProductGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Product model
 */
readonly fields: ProductFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Product.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ProductClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  variants<T extends Prisma.Product$variantsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Product$variantsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$VariantPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Product model
 */
export interface ProductFieldRefs {
  readonly id: Prisma.FieldRef<"Product", 'String'>
  readonly name: Prisma.FieldRef<"Product", 'String'>
  readonly description: Prisma.FieldRef<"Product", 'String'>
  readonly slug: Prisma.FieldRef<"Product", 'String'>
  readonly createdAt: Prisma.FieldRef<"Product", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Product", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Product findUnique
 */
export type ProductFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * Filter, which Product to fetch.
   */
  where: Prisma.ProductWhereUniqueInput
}

/**
 * Product findUniqueOrThrow
 */
export type ProductFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * Filter, which Product to fetch.
   */
  where: Prisma.ProductWhereUniqueInput
}

/**
 * Product findFirst
 */
export type ProductFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * Filter, which Product to fetch.
   */
  where?: Prisma.ProductWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Products to fetch.
   */
  orderBy?: Prisma.ProductOrderByWithRelationInput | Prisma.ProductOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Products.
   */
  cursor?: Prisma.ProductWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Products from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Products.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Products.
   */
  distinct?: Prisma.ProductScalarFieldEnum | Prisma.ProductScalarFieldEnum[]
}

/**
 * Product findFirstOrThrow
 */
export type ProductFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * Filter, which Product to fetch.
   */
  where?: Prisma.ProductWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Products to fetch.
   */
  orderBy?: Prisma.ProductOrderByWithRelationInput | Prisma.ProductOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Products.
   */
  cursor?: Prisma.ProductWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Products from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Products.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Products.
   */
  distinct?: Prisma.ProductScalarFieldEnum | Prisma.ProductScalarFieldEnum[]
}

/**
 * Product findMany
 */
export type ProductFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * Filter, which Products to fetch.
   */
  where?: Prisma.ProductWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Products to fetch.
   */
  orderBy?: Prisma.ProductOrderByWithRelationInput | Prisma.ProductOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Products.
   */
  cursor?: Prisma.ProductWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Products from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Products.
   */
  skip?: number
  distinct?: Prisma.ProductScalarFieldEnum | Prisma.ProductScalarFieldEnum[]
}

/**
 * Product create
 */
export type ProductCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * The data needed to create a Product.
   */
  data: Prisma.XOR<Prisma.ProductCreateInput, Prisma.ProductUncheckedCreateInput>
}

/**
 * Product createMany
 */
export type ProductCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Products.
   */
  data: Prisma.ProductCreateManyInput | Prisma.ProductCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Product createManyAndReturn
 */
export type ProductCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * The data used to create many Products.
   */
  data: Prisma.ProductCreateManyInput | Prisma.ProductCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Product update
 */
export type ProductUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * The data needed to update a Product.
   */
  data: Prisma.XOR<Prisma.ProductUpdateInput, Prisma.ProductUncheckedUpdateInput>
  /**
   * Choose, which Product to update.
   */
  where: Prisma.ProductWhereUniqueInput
}

/**
 * Product updateMany
 */
export type ProductUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Products.
   */
  data: Prisma.XOR<Prisma.ProductUpdateManyMutationInput, Prisma.ProductUncheckedUpdateManyInput>
  /**
   * Filter which Products to update
   */
  where?: Prisma.ProductWhereInput
  /**
   * Limit how many Products to update.
   */
  limit?: number
}

/**
 * Product updateManyAndReturn
 */
export type ProductUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * The data used to update Products.
   */
  data: Prisma.XOR<Prisma.ProductUpdateManyMutationInput, Prisma.ProductUncheckedUpdateManyInput>
  /**
   * Filter which Products to update
   */
  where?: Prisma.ProductWhereInput
  /**
   * Limit how many Products to update.
   */
  limit?: number
}

/**
 * Product upsert
 */
export type ProductUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * The filter to search for the Product to update in case it exists.
   */
  where: Prisma.ProductWhereUniqueInput
  /**
   * In case the Product found by the `where` argument doesn't exist, create a new Product with this data.
   */
  create: Prisma.XOR<Prisma.ProductCreateInput, Prisma.ProductUncheckedCreateInput>
  /**
   * In case the Product was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ProductUpdateInput, Prisma.ProductUncheckedUpdateInput>
}

/**
 * Product delete
 */
export type ProductDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
  /**
   * Filter which Product to delete.
   */
  where: Prisma.ProductWhereUniqueInput
}

/**
 * Product deleteMany
 */
export type ProductDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Products to delete
   */
  where?: Prisma.ProductWhereInput
  /**
   * Limit how many Products to delete.
   */
  limit?: number
}

/**
 * Product.variants
 */
export type Product$variantsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Variant
   */
  select?: Prisma.VariantSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Variant
   */
  omit?: Prisma.VariantOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.VariantInclude<ExtArgs> | null
  where?: Prisma.VariantWhereInput
  orderBy?: Prisma.VariantOrderByWithRelationInput | Prisma.VariantOrderByWithRelationInput[]
  cursor?: Prisma.VariantWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.VariantScalarFieldEnum | Prisma.VariantScalarFieldEnum[]
}

/**
 * Product without action
 */
export type ProductDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Product
   */
  select?: Prisma.ProductSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Product
   */
  omit?: Prisma.ProductOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProductInclude<ExtArgs> | null
}
