# Product Management API Documentation

This document describes the tRPC routers for product management in the Bokul application.

## Overview

The product management system consists of three main entities:
- **Products**: Main product categories (e.g., "Netflix Premium Account")
- **Variants**: Different options for a product (e.g., "1 Month", "3 Months", "6 Months")
- **Stock**: Individual account credentials for each variant

## Router Structure

### 1. Product Router (`/product`)

#### Public Procedures
- `getAll` - Get paginated list of products with search
- `getById` - Get product by ID with variants and stock
- `getBySlug` - Get product by slug with variants and stock

#### Protected Procedures
- `create` - Create new product
- `update` - Update existing product
- `delete` - Delete product (cascades to variants and stock)
- `getStats` - Get product statistics

### 2. Variant Router (`/variant`)

#### Public Procedures
- `getAll` - Get paginated list of variants
- `getByProductId` - Get all variants for a specific product
- `getById` - Get variant by ID with stock and orders

#### Protected Procedures
- `create` - Create new variant
- `update` - Update existing variant
- `delete` - Delete variant (only if no stock/orders exist)
- `getStats` - Get variant statistics

### 3. Stock Router (`/stock`)

#### Protected Procedures Only
- `getAll` - Get paginated list of stock items with filters
- `getByVariantId` - Get all stock items for a variant
- `getById` - Get stock item by ID
- `create` - Create single stock item with auto-generated BOKUL ID
- `createBulk` - Create multiple stock items at once
- `update` - Update stock item details
- `delete` - Delete stock item (only if not sold)
- `assignToOrder` - Assign stock items to an order
- `getAvailableCount` - Get count of available stock for a variant
- `getStats` - Get stock statistics

#### Public Procedures
- `getAvailableCount` - Get available stock count for a variant

## Usage Examples

### Frontend Usage (React with tRPC)

```typescript
import { trpc } from "@/utils/trpc";

// Get all products
const { data: products } = trpc.product.getAll.useQuery({
  page: 1,
  limit: 10,
  search: "netflix"
});

// Create a new product
const createProduct = trpc.product.create.useMutation();

// Get variants for a product
const { data: variants } = trpc.variant.getByProductId.useQuery({
  productId: "product-id"
});

// Create stock items
const createStock = trpc.stock.createBulk.useMutation();
```

### Backend Usage (Server-side)

```typescript
import { appRouter } from "./routers";

// Create tRPC caller
const caller = appRouter.createCaller({ session: userSession });

// Get product stats
const stats = await caller.product.getStats();

// Create variant
const variant = await caller.variant.create({
  name: "1 Month",
  price: 50000,
  productId: "product-id"
});
```

## Data Flow

### Creating a Complete Product Setup

1. **Create Product**
   ```typescript
   const product = await trpc.product.create.mutate({
     name: "Netflix Premium Account",
     description: "Premium Netflix subscription",
     slug: "netflix-premium"
   });
   ```

2. **Create Variants**
   ```typescript
   const variant1Month = await trpc.variant.create.mutate({
     name: "1 Month",
     price: 50000,
     productId: product.id
   });
   ```

3. **Add Stock Items**
   ```typescript
   const stocks = await trpc.stock.createBulk.mutate({
     variantId: variant1Month.id,
     stocks: [
       {
         name: "Premium Account 1",
         email: "<EMAIL>",
         password: "password123"
       },
       // ... more accounts
     ]
   });
   ```

## Stock ID Generation

Stock items automatically get unique IDs in the format `BOKUL-001`, `BOKUL-002`, etc.
The system tracks the last generated ID and increments accordingly.

## Error Handling

All procedures include comprehensive error handling:
- `NOT_FOUND` - Resource doesn't exist
- `CONFLICT` - Duplicate data (e.g., slug, email)
- `BAD_REQUEST` - Invalid operation (e.g., deleting sold stock)
- `UNAUTHORIZED` - Authentication required
- `INTERNAL_SERVER_ERROR` - Unexpected errors

## Pagination

List endpoints support pagination with:
- `page` - Page number (starts from 1)
- `limit` - Items per page (max 100)
- `search` - Optional search term

Response includes pagination metadata:
```typescript
{
  data: [...],
  pagination: {
    page: 1,
    limit: 10,
    total: 50,
    totalPages: 5
  }
}
```

## Security

- All mutation operations require authentication
- Stock management is fully protected
- Product/variant reading is public for storefront
- Sensitive stock data (passwords) only accessible to authenticated users

## Database Relations

```
Product (1) -> (Many) Variant (1) -> (Many) Stock
                     |
                     v
                  (Many) Order
```

- Products can have multiple variants
- Variants can have multiple stock items
- Variants can have multiple orders
- Stock items can be assigned to one order
- Deleting products cascades to variants and stock
- Deleting variants requires no existing stock or orders
