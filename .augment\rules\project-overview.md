---
type: "always_apply"
---

### Bokul Admin-Panel Project Rules for IDE/AI Code Assistance

1. **Strict TypeScript Usage**: All code must be written in TypeScript. No JavaScript files or snippets allowed. Enforce full type safety end-to-end, including inferred types from Prisma schemas via tRPC. If suggesting new code, always include explicit types for variables, functions, and components.

2. **Frontend Constraints**:

   - Use React exclusively for UI components, specially shadcn ui.
   - Routing must use TanStack Router only; no React Router or other alternatives.
   - Styling limited to TailwindCSS classes and shadcn/ui components. No inline styles, CSS modules, or other libraries like Material-UI.
   - Support dark/light themes using Tailwind's built-in theme switching; always include theme-aware classes in suggestions.
   - Components should be functional with hooks; prefer shadcn/ui for reusable UI elements like buttons, modals, or forms.

3. **Backend Constraints**:

   - Server must use Hono for API endpoints.
   - All API communication via tRPC for type-safe procedures; no REST endpoints or GraphQL.
   - Database interactions exclusively through Prisma ORM with PostgreSQL schemas. Queries must use Prisma Client; no raw SQL unless wrapped in Prisma's `$queryRaw`.
   - Authentication handled only with <PERSON> Auth using email/password; no JWT, OAuth, or other providers unless explicitly extending Better Auth.

4. **Architecture and Monorepo Rules**:

   - Maintain monorepo structure: Frontend code in `apps/web`, backend in `apps/server`.
   - All API calls from frontend to backend must be type-safe via tRPC clients; generate tRPC types automatically if needed.
   - Protected routes: Use Better Auth's session management for authentication checks; suggest wrapping routes in TanStack Router's auth loaders.
   - Dashboard features: Any user-related UI (e.g., session management) must integrate with Better Auth's hooks and display user data securely.

5. **Development and Deployment Rules**:

   - Development setup: Code should support hot reload; assume ports localhost:3001 (web) and localhost:3000 (API).
   - Package manager: Use pnpm exclusively; suggestions for dependencies must reference pnpm commands (e.g., `pnpm add`).
   - Deployment: Target Cloudflare Workers for web and Node.js for server; avoid suggestions for other platforms like Vercel or AWS.
   - Monitoring: Include health check endpoints (e.g., `/health` on API) using Hono; suggest simple tRPC procedures for this.

6. **Key Feature Enforcement**:

   - E-commerce admin panel Specifics: For digital products (e.g., Netflix/Spotify accounts), model data with Prisma schemas.
   - Session Management: Always check for user sessions in protected areas; use Better Auth's `useSession` hook in React.
   - Error Handling: Use tRPC's error formatting; suggest try-catch with Prisma errors propagated to UI via toasts (using shadcn/ui).

7. **General Best Practices and Restrictions**:
   - Context-Awareness: All suggestions must reference the project's e-commerce focus (digital products like accounts/subscriptions). Avoid generic code; tailor to bokul's domain (e.g., user purchases, account delivery).
   - Out-of-Context Prevention: If a suggestion doesn't fit (e.g., proposing Next.js), reject it and remind: "This violates bokul's stack—use React with TanStack Router instead."
   - Testing: Encourage unit tests with Vitest (if in stack); integration tests for tRPC endpoints.
